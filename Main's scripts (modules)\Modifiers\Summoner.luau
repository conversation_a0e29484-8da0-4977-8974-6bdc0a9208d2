-- Summoner Modifier Module
-- Allows any enemy to periodically summon other enemies, with all values softcoded/optional.
-- Place this in Modifiers and activate by setting Config["Summoner"] = true or a table of options.

local mob = require(script.Parent.Parent.Enemy)
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local AnimateEnemyEvent = ReplicatedStorage:FindFirstChild("Events") and ReplicatedStorage.Events:FindFirstChild("AnimateEnemy")

local DEFAULTS = {
	Cooldown = 6, -- seconds between summon cycles
	SummonCountMin = 3,
	SummonCountMax = 6,
	SummonName = "Spawn3", -- enemy type to summon
	SummonDelay = 0.2, -- delay between each spawn in a cycle
	SummonCFrame = nil, -- if nil, uses enemy's HRP CFrame
	SummonSound = nil, -- Sound instance or name in enemy to play on summon
	SummonAnim = nil, -- Animation instance or name in Animations folder
	IdleAnim = nil,   -- Animation instance or name in Animations folder
	SummonArgs = {}, -- extra args for mob.Summon
	StopDuringSummon = false, -- if true, set WalkSpeed to 0 during summoning
	SummonSlowdown = nil, -- if set, use this WalkSpeed during summoning
	WaitForMinions = false, -- if true, wait for summoned minions to be destroyed before restoring WalkSpeed
	SummonDistance = 0, -- studs in front of summoner (0 = at HRP)
}

local function getConfigValue(config, key, default)
	if not config then return default end
	local v = config:FindFirstChild(key)
	if v then
		if v:IsA("StringValue") or v:IsA("NumberValue") or v:IsA("BoolValue") then
			return v.Value
		end
	end
	return default
end

local function getAnimName(anim)
	if typeof(anim) == "Instance" and anim:IsA("Animation") then
		return anim.Name
	elseif type(anim) == "string" then
		return anim
	end
	return nil
end

local Summoner = {}

function Summoner.Apply(enemy, value)
	if enemy:GetAttribute("_SummonerActive") then return end -- Prevent double logic
	enemy:SetAttribute("_SummonerActive", true)

	local config = enemy:FindFirstChild("Config")
	local opts = table.clone(DEFAULTS)
	-- 2. Override with config values if present
	if config then
		for k, _ in pairs(DEFAULTS) do
			local cv = getConfigValue(config, k, nil)
			if cv ~= nil then
				opts[k] = cv
			end
		end
	end
	-- 3. Override with table if provided
	if type(value) == "table" then
		for k, v in pairs(value) do
			opts[k] = v
		end
	end

	local function getHRP()
		return enemy:FindFirstChild("HumanoidRootPart") or enemy.PrimaryPart
	end

	coroutine.wrap(function()
		local function playIdleAnim()
			local IdleAnimName = enemy.Animations:FindFirstChild(enemy.Config:FindFirstChild("IdleAnim").Value)
			if IdleAnimName and AnimateEnemyEvent then
				AnimateEnemyEvent:FireAllClients(enemy, IdleAnimName.Name)
			end
		end
		local function stopIdleAnim()
			if AnimateEnemyEvent then
				AnimateEnemyEvent:FireAllClients(enemy, "StopIdleAnim")
			end
		end
		playIdleAnim()
		wait(opts.Cooldown) -- Wait cooldown before first summon
		while enemy.Parent and (not config or not config:FindFirstChild("Disabled") or not config.Disabled.Value) do
			local mapFolder = workspace:FindFirstChild("Map")
			local map = nil
			if mapFolder then
				map = mapFolder:FindFirstChildOfClass("Folder")
			end
			if not map then
				return
			end
			local hrp = getHRP()
			if not hrp then
				return
			end

			local humanoid = enemy:FindFirstChild("Humanoid")
			local originalSpeed = nil
			if humanoid then
				originalSpeed = humanoid.WalkSpeed
			end
			if opts.StopDuringSummon and humanoid then
				humanoid.WalkSpeed = 0
			elseif opts.SummonSlowdown and humanoid then
				humanoid.WalkSpeed = opts.SummonSlowdown
			end

			stopIdleAnim()
			-- Play summon animation if specified
			local summonAnimName = enemy.Animations:FindFirstChild(enemy.Config:FindFirstChild("SummonAnim").Value)
			if summonAnimName and AnimateEnemyEvent then
				AnimateEnemyEvent:FireAllClients(enemy, summonAnimName.Name)
			end

			if opts.SummonSound then
				local sound = nil
				if typeof(opts.SummonSound)=="Instance" then
					sound = opts.SummonSound
				elseif enemy:FindFirstChild("Head") then
					sound = enemy.Head:FindFirstChild(opts.SummonSound)
				end
				if sound and sound:IsA("Sound") then
					sound:Play()
				end
			end

			local numToSpawn = math.random(opts.SummonCountMin, opts.SummonCountMax)
			local spawnedMinions = {}
			for _ = 1, numToSpawn do
				local cframe = nil
				if opts.SummonDistance and opts.SummonDistance ~= 0 then
					local forward = hrp.CFrame.LookVector
					local spawnPos = hrp.Position + forward * opts.SummonDistance
					cframe = CFrame.new(spawnPos, spawnPos + forward)
				else
					cframe = opts.SummonCFrame or hrp.CFrame
				end
				local movingToVal = 1
				if enemy:FindFirstChild("MovingTo") then
					movingToVal = enemy.MovingTo.Value
				end
				local pathIndex = enemy:GetAttribute("PathIndex") or (enemy:FindFirstChild("PathIndex") and enemy.PathIndex.Value) or 1
				local minions = mob.Summon(opts.SummonName, 1, map, cframe, movingToVal, pathIndex, opts.SummonArgs)
				if minions and minions[1] then
					table.insert(spawnedMinions, minions[1])
				end
				task.wait(opts.SummonDelay)
			end

			if opts.WaitForMinions and humanoid then
				humanoid.WalkSpeed = 0
				-- Wait until all minions are destroyed
				local allDead = false
				while not allDead do
					allDead = true
					for _, minion in ipairs(spawnedMinions) do
						if minion and minion.Parent and minion:FindFirstChild("Humanoid") and minion.Humanoid.Health > 0 then
							allDead = false
							break
						end
					end
					task.wait(0.5)
				end
			end

			-- Restore WalkSpeed after summoning
			if humanoid and originalSpeed then
				humanoid.WalkSpeed = originalSpeed
			end

			playIdleAnim()
			wait(opts.Cooldown)
		end
	end)() -- Close coroutine.wrap function
end

return Summoner
