-- SummonerConfigCloner
-- Place this in ServerScriptService. Use to clone the SummonerConfigPreset to an enemy.

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local SummonerConfigPreset = require(ReplicatedStorage:WaitF<PERSON><PERSON>hild("SummonerConfigPreset"))

-- Example usage: set this to the enemy you want to give the config to
local enemy = workspace.Enemy:FindFirstChild("YourEnemyNameHere")

if enemy and not enemy:FindFirstChild("Config") then
	local configClone = SummonerConfigPreset:Clone()
	configClone.Parent = enemy
	print("Summoner config cloned to", enemy.Name)
else
	print("Enemy not found or already has Config")
end
