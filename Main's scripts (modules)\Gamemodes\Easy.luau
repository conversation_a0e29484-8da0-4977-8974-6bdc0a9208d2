local function GetEasy(wave, map, context)
	local SpawnEnemy = context.SpawnEnemy
	local PlayMusic = context.PlayMusic
	local StopMusic = context.StopMusic
	local info = context.info
	local LastWave = context.LastWave
	local Wavecount = context.Wavecount
	-- ...existing code from round.GetEasy...
		if wave <= 2 then
		SpawnEnemy("Credulous", 2 * wave, map)
	elseif wave <= 4 then
		SpawnEnemy("Credulous", 3 * wave / 2, map)
		SpawnEnemy("Runner", 2 * wave / 2, map)
	elseif wave == 5 then
		SpawnEnemy("Credulous", 10, map)
		task.wait(1)
		SpawnEnemy("Prepared", 1, map)
		task.wait(1)
		SpawnEnemy("Prepared", 1, map)
		task.wait(2)
		SpawnEnemy("Runner", 4, map)
	elseif wave <= 9 then
		SpawnEnemy("Prepared", 3 * wave/3, map)
		task.wait(1)
		SpawnEnemy("Runner", 5 * (wave / 3), map)
		task.wait(1)
		SpawnEnemy("Credulous",1,map)
	elseif wave == 10 then
		SpawnEnemy("Prepared", 5, map)
		task.wait(3)
		SpawnEnemy("Prepared", 5, map)
		task.wait(4)
		SpawnEnemy("Champion",1,map, {IsBoss = true})
	elseif wave <= 12 then
		SpawnEnemy("Prepared", 5, map)
		task.wait(3)
		SpawnEnemy("Prepared", 5 * wave / 7, map)
		task.wait(4)
		SpawnEnemy("Credulous",2,map)
		task.wait(4)
		SpawnEnemy("Credulous",7,map)
	elseif wave == 13 then
		SpawnEnemy("Prepared", 5, map, {IsHidden = true})
		task.wait(3)
		SpawnEnemy("Credulous",1,map)
	elseif wave == 14 then
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared",10,map)
	elseif wave == 15 then
		SpawnEnemy("Prepared", 20, map, {IsHidden = true})
	elseif wave <= 18 then
		SpawnEnemy("Armored", 5, map)
		task.wait(2)
		SpawnEnemy("Prepared", 5, map)
		task.wait(2)
		SpawnEnemy("Prepared", 3, map,{IsHidden = true})
		task.wait(2)
		SpawnEnemy("Champion", 1, map)
		task.wait(5)
		SpawnEnemy("Champion", 1, map)
	elseif wave == 19 then
		SpawnEnemy("Armored", 4, map)
		task.wait(2)
		SpawnEnemy("Prepared", 10, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Swarmcore", 1, map,{IsBoss = true})
	elseif wave <= 21 then
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(4)
		SpawnEnemy("Prepared", 7, map,{IsHidden = true})
	elseif wave <= 23 then
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(4)
		SpawnEnemy("Shockwrecker", 1, map,{IsBoss = true})
	elseif wave == 24 then
		-- act 1
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(4)
		SpawnEnemy("Raider", 1, map,{IsBoss = true})
		-- act 2
		task.wait(math.random(10,15))
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared", 4, map)
		task.wait(4)
		SpawnEnemy("Raider", 1, map)
	elseif wave <= 26 then
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared", 9, map)
		task.wait(4)
		SpawnEnemy("Swarmcore", 2, map)
	elseif wave == 27 then
		SpawnEnemy("Champion", 2, map,{IsHidden = true})
		task.wait(1)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared", 9, map,{IsHidden = true})
		task.wait(4)
		SpawnEnemy("Swarmcore", 1, map,{NoStun = true})
	elseif wave == 28 then
		PlayMusic("Reckoning")
		SpawnEnemy("Champion", 2, map,{IsHidden = true})
		task.wait(1)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Raider", 1, map)
		task.wait(4)
		SpawnEnemy("Sentinel", 1, map,{IsBoss = true})
	elseif wave == 29 then
		StopMusic()
		task.wait()
		info.Min.Value = 2
		SpawnEnemy("Champion", 10, map)
		task.wait(2)
		SpawnEnemy("Armed", 5,map)
		task.wait(2)
		SpawnEnemy("Runner",10,map)
		task.wait(2)
		SpawnEnemy("Champion", 5, map)
		task.wait(5)
		SpawnEnemy("Raider",2,map,{IsHidden = true})
		task.wait(10)
		--act 2
		SpawnEnemy("Champion", 5, map)
		task.wait(math.random(3,7))
		SpawnEnemy("Champion", 2, map)
		SpawnEnemy("Prepared", 10, map,{IsHidden = true})
		task.wait(3)
		SpawnEnemy("Armed",3,map)
	elseif wave == Wavecount then
		PlayMusic("Metal Storm")
		LastWave.Value = true
		SpawnEnemy("Champion", 7, map)
		task.wait(2)
		SpawnEnemy("Armed", 9, map)
		task.wait(1)
		SpawnEnemy("Prepared", 10)
		task.wait(6)
		SpawnEnemy("Raider", 2, map)
		task.wait(3)
		SpawnEnemy("Armored", 5, map)
		task.wait(4)
		SpawnEnemy("Eclipse Enforcer",1,map,{IsBoss = true, DefensePercent = 0})
	end
end

return GetEasy
