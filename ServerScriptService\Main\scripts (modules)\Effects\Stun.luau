local ReplicatedStorage = game:GetService("ReplicatedStorage")
local DebrisService = game:GetService("Debris")

local Particles = ReplicatedStorage:WaitForChild("Particles")

print("Stun effect module loaded!")

local StunEffect = {}

function StunEffect.Apply(tower, target, duration)
    print("StunEffect.Apply called with target:", target.Name, "duration:", duration)
    
    local humanoid = target:FindFirstChild("Humanoid")
    
    if not humanoid then 
        print("Target has no humanoid")
        return false
    end
    
    -- Check if target can be affected (boss, immunity, etc.)
    if target.Config:FindFirstChild("NoStun") and target.Config.NoStun.Value == true then
        print("Target has NoStun immunity")
        return false
    end
    
    if target.Config:FindFirstChild("IsBoss") and target.Config.IsBoss.Value == true then
        print("Target is a boss and immune to stun")
        return false
    end
    
    local StunParticle = Particles:FindFirstChild("StunEffect"):Clone()
    local stunAnim = ReplicatedStorage:WaitForChild("MiscAnims"):WaitForChild("StunAnim")
    
    local animator = humanoid:FindFirstChildOfClass("Animator") or Instance.new("Animator", humanoid)
    local animationTrack = animator:LoadAnimation(stunAnim)

    if not target.Torso:FindFirstChild("StunEffect") and not target.HumanoidRootPart:FindFirstChild("StunEffect") then
        StunParticle.Parent = target.Torso or target.HumanoidRootPart
    end
    
    local originalSpeed = humanoid.WalkSpeed
    humanoid.WalkSpeed = humanoid.WalkSpeed / 5

    animationTrack:Play()

    task.spawn(function()
        task.wait(duration)
        if target:FindFirstChildOfClass("Humanoid") then
            DebrisService:AddItem(StunParticle, duration)
            animationTrack:Stop()
            humanoid.WalkSpeed = target.Config.NormalSpeed.Value 
        end
    end)
    
    print("Stun effect successfully applied to", target.Name)
    return true
end

return StunEffect
