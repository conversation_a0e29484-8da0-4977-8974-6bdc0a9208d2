-- ExplosiveDeath module: Attach to any enemy to give it explosive-on-death behavior
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local DebrisService = game:GetService("Debris")
local ExplosionTemplate = ReplicatedStorage.Misc:WaitForChild("Explosion")

local ExplosiveDeath = {}

--[[
	Usage:
	ExplosiveDeath.attach(enemy, {
		explosionRadius = 10,
		stunDuration = 3,
		TypeOfDebuff = "Stunned"
	})
	If options are omitted, defaults are used.
]]

function ExplosiveDeath.attach(enemy, options)
	options = options or {}
	local explosionRadius = options.explosionRadius or 10
	local stunDuration = options.stunDuration or 3
	local TypeOfDebuff = options.TypeOfDebuff or "Stunned"

	local function createExplosion(position)
		local explosion = ExplosionTemplate:Clone()
		explosion.Position = position
		explosion.Size = Vector3.new(1, 1, 1)
		explosion.Parent = workspace.Particles
		explosion.Explode:Play()

		local tweenInfo = TweenInfo.new(0.5, Enum.EasingStyle.Linear, Enum.EasingDirection.Out)
		local goal = { Size = Vector3.new(explosionRadius, explosionRadius, explosionRadius) }
		local endgoal = {Transparency = 1}
		local tween = TweenService:Create(explosion, tweenInfo, goal)
		local tween2 = TweenService:Create(explosion, tweenInfo, endgoal)
		tween:Play()
		tween2:Play()
		DebrisService:AddItem(explosion, 1)
	end

	local function stunTowers(position)
		for _, tower in ipairs(workspace.Towers:GetChildren()) do
			if tower:FindFirstChild("Humanoid") and (tower.HumanoidRootPart.Position - position).Magnitude <= explosionRadius then
				local Stunned = Instance.new("NumberValue")
				Stunned.Name = TypeOfDebuff
				Stunned.Value = stunDuration
				Stunned.Parent = tower.Config.Debuffs
			end
		end
	end

	if enemy and enemy:FindFirstChild("Humanoid") and enemy:FindFirstChild("PrimaryPart") then
		enemy.Humanoid.Died:Connect(function()
			local explosionPosition = enemy.PrimaryPart.Position
			stunTowers(explosionPosition)
			createExplosion(explosionPosition)
		end)
	end
end

return ExplosiveDeath
