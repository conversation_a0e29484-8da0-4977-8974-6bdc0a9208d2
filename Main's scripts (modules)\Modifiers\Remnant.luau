local HttpService = game:GetService("HttpService")

local Remnant = {}

-- Configuration for the Remnant modifier - easily customizable
local REMNANT_CONFIG = {
	-- Config value names (what to look for in enemy Config folder)
	SpawnTypeKey = "DeathEnemyType",      -- StringValue: what enemy type to spawn
	SpawnAmountKey = "DeathEnemyAmount",  -- NumberValue: how many to spawn
	ModifiersKey = "DeathModifiers",      -- StringValue: JSON modifiers for spawned enemies

	-- Attribute names (internal storage)
	EnabledAttribute = "_RemnantEnabled",
	SpawnTypeAttribute = "_RemnantSpawnType",
	SpawnQuantityAttribute = "_RemnantSpawnQuantity",
	ModifiersAttribute = "_RemnantModifiers",

	-- Default values
	DefaultSpawnAmount = 1,
	DefaultSpawnType = "Spawn2",

	-- Debug settings
	EnableDebugPrints = true,
}

-- Helper function for debug printing
local function debugPrint(...)
	if REMNANT_CONFIG.EnableDebugPrints then
		print("[Remnant]", ...)
	end
end

-- Helper function to get config value with fallback
local function getConfigValue(config, keyName, defaultValue)
	local configValue = config:FindFirstChild(keyName)
	if configValue and configValue.Value ~= nil and configValue.Value ~= "" then
		return configValue.Value
	end
	return defaultValue
end

-- Apply function called by ModifierManager when Remnant modifier is enabled
function Remnant.Apply(enemy, enabled)
	debugPrint("Apply called for enemy:", enemy and enemy.Name or 'nil', "enabled:", enabled)

	if not enemy or not enemy:IsA("Model") then
		warn("[Remnant] Invalid enemy model passed.")
		return false
	end

	local config = enemy:FindFirstChild("Config")
	if not config then
		warn("[Remnant] No Config found in enemy: " .. enemy.Name)
		return false
	end

	-- Set the Remnant config value
	local remnantValue = config:FindFirstChild("Remnant")
	if not remnantValue then
		remnantValue = Instance.new("BoolValue")
		remnantValue.Name = "Remnant"
		remnantValue.Parent = config
	end
	remnantValue.Value = enabled and true or false

	-- If enabled, set up the death spawning configuration
	if enabled then
		-- Get spawn type (required)
		local spawnType = getConfigValue(config, REMNANT_CONFIG.SpawnTypeKey, REMNANT_CONFIG.DefaultSpawnType)
		if not spawnType or spawnType == "" then
			warn("[Remnant] No spawn type defined for:", enemy.Name, "- using default:", REMNANT_CONFIG.DefaultSpawnType)
			spawnType = REMNANT_CONFIG.DefaultSpawnType
		end

		-- Get spawn amount (optional, defaults to configured default)
		local spawnAmount = getConfigValue(config, REMNANT_CONFIG.SpawnAmountKey, REMNANT_CONFIG.DefaultSpawnAmount)

		-- Get modifiers (optional)
		local modifiersString = getConfigValue(config, REMNANT_CONFIG.ModifiersKey, nil)
		local modifiers = nil

		if modifiersString then
			local success, result = pcall(function()
				return HttpService:JSONDecode(modifiersString)
			end)
			if success then
				modifiers = result
				debugPrint("Parsed modifiers for", enemy.Name, ":", modifiers)
			else
				warn("[Remnant] Failed to decode modifiers for", enemy.Name, ":", tostring(result))
			end
		end

		-- Store death spawn data as attributes on the enemy to avoid circular dependency
		enemy:SetAttribute(REMNANT_CONFIG.EnabledAttribute, true)
		enemy:SetAttribute(REMNANT_CONFIG.SpawnTypeAttribute, spawnType)
		enemy:SetAttribute(REMNANT_CONFIG.SpawnQuantityAttribute, spawnAmount)

		-- Store modifiers as JSON string if present
		if modifiers then
			local success, jsonString = pcall(function()
				return HttpService:JSONEncode(modifiers)
			end)
			if success then
				enemy:SetAttribute(REMNANT_CONFIG.ModifiersAttribute, jsonString)
			else
				warn("[Remnant] Failed to encode modifiers as JSON:", tostring(jsonString))
			end
		end

		debugPrint("Configured death spawn for", enemy.Name, "- Type:", spawnType, "Amount:", spawnAmount)
	else
		-- Clear attributes when disabled
		enemy:SetAttribute(REMNANT_CONFIG.EnabledAttribute, nil)
		enemy:SetAttribute(REMNANT_CONFIG.SpawnTypeAttribute, nil)
		enemy:SetAttribute(REMNANT_CONFIG.SpawnQuantityAttribute, nil)
		enemy:SetAttribute(REMNANT_CONFIG.ModifiersAttribute, nil)
		debugPrint("Disabled death spawn for", enemy.Name)
	end

	return true
end

-- Export configuration for use by other modules if needed
Remnant.Config = REMNANT_CONFIG

return Remnant
