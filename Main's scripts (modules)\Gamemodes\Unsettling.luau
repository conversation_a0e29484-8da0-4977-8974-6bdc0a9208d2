local function GetUnsettling(wave, map, context)
	local SpawnEnemy = context.SpawnEnemy
	local PlayMusic = context.PlayMusic
	local StopMusic = context.StopMusic
	local info = context.info
	local LastWave = context.LastWave
	local Wavecount = context.Wavecount
	for _, v in pairs(game.Players:GetPlayers()) do
		v.Gold.Value += 0
	end
	if wave == 1 then
		PlayMusic("IntoMist")
		SpawnEnemy("Corruptling",1,map, {HealthValue = 16, Health = true, Fortified = true})
		task.wait(5)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 16, Health = true})
		task.wait(5)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 16, Health = true})
		task.wait(5)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 16, Health = true})
		task.wait(5)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 16, Health = true})
	elseif wave == 2 then
		SpawnEnemy("Corruptling",1,map, {HealthValue = 17, Health = true})
		task.wait(3)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 18, Health = true})
		task.wait(3.5)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 19, Health = true})
		task.wait(4)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 20, Health = true})
		task.wait(4.5)
		SpawnEnemy("Corruptling",1,map, {HealthValue = 21, Health = true})
	elseif wave == 3 then
		SpawnEnemy("Corruptling",2,map)
		task.wait(5)
		SpawnEnemy("Trapped Soul",1,map, {HealthValue = 61, Health = true})
		task.wait(2)
		SpawnEnemy("Corruptling",6,map, {HealthValue = 5, Health = true})
	elseif wave <= 5 then
		if wave == 4 then
			SpawnEnemy("Corruptling",2,map)
		else
			SpawnEnemy("Corruptling",2,map, {Petrified = true})
		end
		task.wait(5)
		SpawnEnemy("Trapped Soul",1,map, {HealthValue = 80, Health = true})
		if wave == 5 then
			task.wait(2)
			SpawnEnemy("Corruptling",3,map, { Fleetfooted = true})
		end
		task.wait(2)
		SpawnEnemy("Corruptling",3,map, {HealthValue = 30, Health = true})
		if wave == 5 then
			task.wait(2)
			SpawnEnemy("Corruptling",3,map, {HealthValue = 36, Health = true})
		end
	elseif wave == 6 then
		SpawnEnemy("Corruptling",9,map,{Fortified = true, Petrified = true})
		task.wait(5)
		SpawnEnemy("Gluttony",1,map,{IsBoss = true})
		SpawnEnemy("Trapped Soul",1,map,{Armed = "Glock", Petrified = true})
	elseif wave == 7 then
		SpawnEnemy("Corruptling",9,map,{Armed = "Corrupted Crystal", Petrified = true})
		task.wait(2)
		SpawnEnemy("Corruptling",9,map,{Petrified = true, Health = true, HealthValue = 142})
		SpawnEnemy("Trapped Soul",4,map,{Armed = "Corrupted Crystal", Petrified = true})
	end
end

return GetUnsettling
