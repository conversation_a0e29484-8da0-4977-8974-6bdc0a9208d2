local PhysicsService = game:GetService("PhysicsService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local DebrisService = game:GetService("Debris")
local TweenService = game:GetService("TweenService")
local Players = game:GetService("Players")

local events = ReplicatedStorage:WaitForChild("Events")
local animateTowerEvent = events:WaitForChild("AnimateTower")
local ActivateAbilityEvent = events:WaitForChild("ActivateTowerAbility")
local animateTowerChargeEvent = events:WaitForChild("AnimateTowerCharge")
local messageEvent = events:WaitForChild("MessageEvent")
local functions = ReplicatedStorage:WaitForChild("Functions")
local spawnTowerFunction = functions:WaitForChild("SpawnTower")
local requestTowerFunction = functions:WaitForChild("RequestTower")
local sellTowerFunction = functions:WaitForChild("SellTower")
local changeModeFunction = functions:WaitForChild("ChangeTowerMode")

local Particles = ReplicatedStorage:WaitForChild("Particles")
local TowerFunctions = require(script.Parent["TowerFunctions"])
local EffectManager = require(script.Parent["EffectsManager"])
local TowerAttributesManager = require(script.Parent["TowerAttributesManager"])
local TowerTargeting = require(ReplicatedStorage:WaitForChild("Modules"):WaitForChild("TowerTargetting"))
local TowerAbilitiesManager = require(script.Parent["TowerAbilitiesManager"])

local maxTowers = 20
local tower = {}
local IndividualTowersOnLimit = {}

-- Function to apply damage, handle defense, shield, and cash logic
local function ApplyTowerDamage(target, config, player)
	local damage = config.Damage.Value
	local defensePercent = target.Config:FindFirstChild("DefensePercent")
	if defensePercent then
		damage = math.round(damage - (damage * (defensePercent.Value / 100)))
	end

	local shield = target.Config:FindFirstChild("ShieldHealth")
	local humanoid = target:FindFirstChild("Humanoid")
	if not humanoid then return end

	if shield and shield.Value > 0 then
		if damage <= shield.Value then
			shield.Value = shield.Value - damage
			damage = 0
		else
			damage = damage - shield.Value
			shield.Value = 0
		end
	end

	if damage > 0 then
		humanoid:TakeDamage(damage)
	end
end

tower.ApplyTowerDamage = ApplyTowerDamage

function tower.Optimize(towerToOptimize)
	local humanoid = towerToOptimize:FindFirstChild("Humanoid")


	if not humanoid then return end
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Seated, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Running, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Climbing, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Landed, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
end

function tower.Attack(newTower, player)
	local function doAttack(target)
		local config = newTower:WaitForChild("Config")
		if target == "NO_TARGET" or typeof(target) ~= "Instance" then
			return
		end
		if target and target:FindFirstChild("Humanoid") and target.Humanoid.Health > 0 then
			EffectManager.FaceTarget(newTower, target, 0.07)
			if newTower.Config:FindFirstChild("Debuffs") and newTower.Config:WaitForChild("Debuffs"):FindFirstChild("Stunned") then
				local StunnedParticle = ReplicatedStorage.Particles.StunParticle:Clone()
				StunnedParticle.Parent = newTower:WaitForChild("Head")
				StunnedParticle.Enabled = true
				wait(newTower.Config.Debuffs.Stunned.Value)
				StunnedParticle:Destroy()
				newTower.Config.Debuffs:ClearAllChildren()
			else
				local targetCFrame = CFrame.lookAt(newTower.HumanoidRootPart.Position, target.HumanoidRootPart.Position)
				if newTower.Head:FindFirstChild("Shoot") then
					newTower.Head.Shoot:Play()
				end
				animateTowerEvent:FireAllClients(newTower, "Attack", target)
				EffectManager.ApplyEffects(newTower, target)
				ApplyTowerDamage(target, config, player)
				TowerFunctions.HandleRepeatMoney(target, player)
				local noMoney = target.Config:FindFirstChild("NoMoney")
				if target.Humanoid.Health <= 0 and (not noMoney or noMoney.Value == false) then
					player.Kills.Value += 1
					local cash = TowerFunctions.GetKillCash(target)
					TowerFunctions.SendCash(player, cash)
				end
				task.wait(config.Cooldown.Value)
			end
		end
	end
	local config = newTower:WaitForChild("Config")
	local wrappedAttack = TowerAttributesManager.ApplyAttributes(newTower, config, doAttack)

	while newTower and newTower.Parent do
		local target = TowerTargeting.FindTarget(newTower, config.Range.Value, config.TargetMode.Value)
		if target then
			wrappedAttack(target)
		else
			wrappedAttack("NO_TARGET")
		end
		task.wait()
	end
end


function tower.ChangeMode(player, model)
	if model and model:FindFirstChild("Config") then
		local targetMode = model.Config.TargetMode
		local modes = {"First", "Last", "Strongest", "Weakest", "Nearest","Random"}
		local modeIndex = table.find(modes, targetMode.Value)

		if modeIndex < #modes then
			targetMode.Value = modes[modeIndex + 1]
		else
			targetMode.Value = modes[1]
		end

		return true
	else
		warn("Unable to change tower mode")
		return false
	end
end
changeModeFunction.OnServerInvoke = tower.ChangeMode

function tower.Sell(player, model)
	if model and model:FindFirstChild("Config") then
		if model.Config.Owner.Value == player.Name then
			local towerName = model.Config.OriginalTower.Value
			player.PlacedTowers.Value -= 1

			-- Decrease the count for the specific tower type
			if IndividualTowersOnLimit[player.Name][towerName] then
				IndividualTowersOnLimit[player.Name][towerName] -= 1
			end

			player.Gold.Value += model.Config.Spent.Value / 3
			model:Destroy()
			return true
		end
	end

	warn("Unable to sell this tower")
	return false
end

sellTowerFunction.OnServerInvoke = tower.Sell

function tower.Spawn(player, name, cframe, previous)
	-- Ensure 'name' is a string
	if typeof(name) ~= "string" then
		warn("Expected 'name' to be a string, but received: " .. typeof(name))
		return false
	end

	local allowedToSpawn = tower.CheckSpawn(player, name, previous)

	if allowedToSpawn then
		local newTower
		local oldMode = nil
		local oldDamageDealt = 0
		local oldSpent = 0
		local oldOriginalTower = nil

		if previous then
			-- Collect existing values from the previous tower
			oldMode = previous.Config.TargetMode.Value 
			oldDamageDealt = previous.Config.DamageDealt.Value
			oldSpent = previous.Config.Spent.Value
			oldOriginalTower = previous.Config.OriginalTower.Value  -- Preserve original tower name
			if IndividualTowersOnLimit[player.Name][oldOriginalTower] then
				IndividualTowersOnLimit[player.Name][oldOriginalTower] -= 1
			end
			previous:Destroy()  -- Destroy previous tower if upgrading
			newTower = ReplicatedStorage.Towers.Upgrades[name]:Clone()  -- Clone upgraded tower
		else
			newTower = ReplicatedStorage.Towers[name]:Clone()  -- Clone original tower
			player.PlacedTowers.Value += 1  -- Increment the player's tower count
		end

		-- Set up the new tower's configuration
		local ownerValue = Instance.new("StringValue")
		ownerValue.Name = "Owner"
		ownerValue.Value = player.Name
		ownerValue.Parent = newTower.Config

		local targetMode = Instance.new("StringValue")
		targetMode.Name = "TargetMode"
		targetMode.Value = oldMode or "First"  -- Use existing mode or default to "First"
		targetMode.Parent = newTower.Config

		local DamageDealt = Instance.new("NumberValue")
		DamageDealt.Name = "DamageDealt"
		DamageDealt.Value = oldDamageDealt or 0
		DamageDealt.Parent = newTower.Config

		local Spent = Instance.new("NumberValue")
		Spent.Name = "Spent"
		Spent.Value = oldSpent + (newTower.Config.Price.Value or 0)  -- Calculate total spent
		Spent.Parent = newTower.Config

		local OriginalTower = newTower.Config:FindFirstChild("OriginalTower")
		if not OriginalTower then
			OriginalTower = Instance.new("StringValue")
			OriginalTower.Name = "OriginalTower"
			OriginalTower.Value = oldOriginalTower or name  -- Set to original if upgrading or current name
			OriginalTower.Parent = newTower.Config
		else
			OriginalTower.Value = oldOriginalTower or name  -- Ensure correct original name
		end

		-- Set MaxTowers based on the original tower
		local originalTowerConfig = previous and ReplicatedStorage.Towers[oldOriginalTower] or ReplicatedStorage.Towers[name]
		local IndPlacementLimit = originalTowerConfig:FindFirstChild("MaxTowers")

		if not IndPlacementLimit then
			IndPlacementLimit = Instance.new("IntValue")
			IndPlacementLimit.Name = "MaxTowers"
			IndPlacementLimit.Value = 200
			IndPlacementLimit.Parent = newTower.Config
		else
			local maxTowersValue = IndPlacementLimit.Value
			local maxTowersInstance = Instance.new("IntValue")
			maxTowersInstance.Name = "MaxTowers"
			maxTowersInstance.Value = maxTowersValue
			maxTowersInstance.Parent = newTower.Config
		end

		-- Position and parent the new tower
		newTower:SetPrimaryPartCFrame(cframe)
		-- Anchor the HumanoidRootPart upon spawning
		local hrp = newTower:FindFirstChild("HumanoidRootPart")
		if hrp then
			hrp.Anchored = true
		end
		newTower.Parent = workspace.Towers
		tower.Optimize(newTower)

		-- Set collision group
		for _, object in ipairs(newTower:GetDescendants()) do
			if object:IsA("BasePart") then
				object.CollisionGroup = "Tower"
			end
		end

		-- Create boundary part for tower
		local height = (newTower.PrimaryPart.Size.Y / 2) + newTower.PrimaryPart.Size.Y 
		local offset = Vector3.new(0, -height, 0)
		local boundaryPart = Instance.new("Part")
		boundaryPart.Name = "Boundary"
		boundaryPart.Shape = Enum.PartType.Cylinder
		boundaryPart.Transparency = 1
		boundaryPart.BrickColor = BrickColor.new("Persimmon")
		boundaryPart.Size = Vector3.new(0.3, newTower.Config.BoundarySize.Value, newTower.Config.BoundarySize.Value)
		boundaryPart.Material = Enum.Material.Neon
		boundaryPart.TopSurface = Enum.SurfaceType.Smooth
		boundaryPart.BottomSurface = Enum.SurfaceType.Smooth
		boundaryPart.CanCollide = false
		boundaryPart.CanQuery = true
		boundaryPart.Parent = newTower
		boundaryPart.Anchored = true
		boundaryPart.Position = newTower.PrimaryPart.Position + offset
		boundaryPart.CFrame = newTower.PrimaryPart.CFrame * CFrame.new(offset)
		boundaryPart.Orientation = Vector3.new(0,0,90)

		-- Deduct the cost from the player's gold
		player.Gold.Value -= newTower.Config.Price.Value 

		-- Increment the count for the specific tower type
		local towerName = previous and oldOriginalTower or name
		IndividualTowersOnLimit[player.Name][towerName] = (IndividualTowersOnLimit[player.Name][towerName] or 0) + 1

		-- Start attacking if necessary
		task.spawn(function()
			tower.Attack(newTower, player)
		end)
		return newTower
	else
		warn("Requested tower does not exist:", name)
		return false
	end
end


spawnTowerFunction.OnServerInvoke = tower.Spawn


local function initializePlayerData(player)
	IndividualTowersOnLimit[player.Name] = {}
	for _, tower in ipairs(ReplicatedStorage.Towers:GetChildren()) do
		IndividualTowersOnLimit[player.Name][tower.Name] = 0
	end
end

-- Hook player initialization
Players.PlayerAdded:Connect(initializePlayerData)

function tower.CheckSpawn(player, name, previous)
	if type(name) ~= "string" then
		warn("Expected 'name' to be a string, but received: " .. type(name))
		return false
	end

	local towerExists = ReplicatedStorage.Towers:FindFirstChild(name, true)

	if towerExists then
		-- Check if the player can afford the tower
		if towerExists.Config.Price and towerExists.Config.Price:IsA("IntValue") then
			if towerExists.Config.Price.Value <= player.Gold.Value then
				-- Check individual tower limits
				local individualMaxTowers = towerExists.Config:FindFirstChild("MaxTowers")
				local maxAllowed = individualMaxTowers and individualMaxTowers.Value or 999
				local currentTowers = IndividualTowersOnLimit[player.Name][name] or 0

				-- Debug prints for checking values
				print("Max allowed for tower " .. name .. ": " .. maxAllowed)
				print("Current towers placed by player " .. player.Name .. ": " .. currentTowers)

				if previous or currentTowers < maxAllowed then
					if player.Alive.Value == true then
						if previous or player.PlacedTowers.Value < maxTowers then
							print()
							return true 
						else
							messageEvent:FireClient(player, "Player has reached max limit of towers.")
						end
					else
						messageEvent:FireClient(player, "Can't place tower while dead.")
					end
				else
					messageEvent:FireClient(player, "Player has reached max limit for this tower.")
					warn("Player has reached max limit for this tower: " .. player.Name)
				end
			else
				messageEvent:FireClient(player, "Player cannot afford.")
				warn("Player cannot afford: " .. player.Name)
			end
		else
			warn("Tower price is not a valid IntValue.")
		end
	else
		warn("Requested tower does not exist: " .. name)
	end
	return false
end

requestTowerFunction.OnServerInvoke = tower.CheckSpawn

-- Server-side placement validation for towers
-- Arguments:
--   towerModel: the tower model to place (should have PlacementType StringValue)
--   cframe: CFrame of intended placement
--   map: the map folder (should contain TowerArea)
function tower.IsValidPlacement(towerModel, cframe, map)
	local towerArea = map and map:FindFirstChild("TowerArea")
	if not towerArea then return false end

	-- Raycast down from above the intended position to find the placement part
	local rayOrigin = cframe.Position + Vector3.new(0, 10, 0)
	local rayDirection = Vector3.new(0, -20, 0)
	local raycastParams = RaycastParams.new()
	raycastParams.FilterDescendantsInstances = {towerModel}
	raycastParams.FilterType = Enum.RaycastFilterType.Exclude

	local result = workspace:Raycast(rayOrigin, rayDirection, raycastParams)
	if not result or not result.Instance then return false end

	-- Must hit a part under TowerArea
	if result.Instance.Parent ~= towerArea then return false end

	-- Robustly find which subfolder (if any) this part is in (supports any depth)
	local subAreaType = nil
	local current = result.Instance
	while current and current ~= towerArea do
		if current.Parent and current.Parent:IsA("Folder") and current.Parent.Parent == towerArea then
			subAreaType = current.Parent.Name
			break
		end
		current = current.Parent
	end

	-- Determine placement type
	local placementType = "Ground"
	if towerModel:FindFirstChild("PlacementType") and towerModel.PlacementType:IsA("StringValue") then
		placementType = towerModel.PlacementType.Value
	end

	-- Placement rules
	if subAreaType then
		if placementType ~= subAreaType then
			return false
		end
	else
		return false -- Not in a subfolder, disallow placement
	end

	-- Check if the surface normal is approximately up
	local towerAreaNormal = Vector3.new(0, 1, 0)
	if (result.Normal - towerAreaNormal).Magnitude > 0.1 then
		return false
	end

	return true
end

-- Listen for ability activation requests from clients
ActivateAbilityEvent.OnServerEvent:Connect(function(player, tower)
	if typeof(tower) ~= "Instance" or not tower:IsDescendantOf(workspace.Towers) then return end
	local config = tower:FindFirstChild("Config")
	if not config or not config:FindFirstChild("Owner") or config.Owner.Value ~= player.Name then return end
	local success, err = TowerAbilitiesManager.ActivateAbility(tower, player)
	if not success then
		warn("Ability activation failed:", err)
	end
end)

return tower
