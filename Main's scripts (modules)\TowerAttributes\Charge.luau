return function(attackFunc, tower, config)
	return function(target, ...)
		-- Ensure all required state objects exist
		if not config:FindFirstChild("Charged") then
			local charged = Instance.new("BoolValue")
			charged.Name = "Charged"
			charged.Value = false
			charged.Parent = config
		end
		if not config:FindFirstChild("IsCharging") then
			local isCharging = Instance.new("BoolValue")
			isCharging.Name = "IsCharging"
			isCharging.Value = false
			isCharging.Parent = config
		end
		if not config:FindFirstChild("IsCoolingDown") then
			local isCoolingDown = Instance.new("BoolValue")
			isCoolingDown.Name = "IsCoolingDown"
			isCoolingDown.Value = false
			isCoolingDown.Parent = config
		end
		if not config:FindFirstChild("ChargeTime") then
			local chargeTime = Instance.new("NumberValue")
			chargeTime.Name = "ChargeTime"
			chargeTime.Value = 1
			chargeTime.Parent = config
		end
		if not config:FindFirstChild("Charging") then
			local charging = Instance.new("NumberValue")
			charging.Name = "Charging"
			charging.Value = 0
			charging.Parent = config
		end

		local chargeTime = config.ChargeTime.Value
		local charging = config.Charging
		local isCharging = config.IsCharging
		local charged = config.Charged
		local isCoolingDown = config.IsCoolingDown

		-- If no target, always clear charge state and exit
		if not target or target == "NO_TARGET" then
			charged.Value = false
			isCharging.Value = false
			isCoolingDown.Value = false
			charging.Value = 0
			return
		end

		-- If charging or cooling down, do not proceed
		if isCharging.Value or isCoolingDown.Value then
			return
		end

		-- Charging logic: if not charged, start charging
		if not charged.Value then
			isCharging.Value = true
			charging.Value = 0

			local ReplicatedStorage = game:GetService("ReplicatedStorage")
			local events = ReplicatedStorage:FindFirstChild("Events")
			local animateTowerChargeEvent = events and events:FindFirstChild("AnimateTowerCharge")
			if animateTowerChargeEvent then
				animateTowerChargeEvent:FireAllClients(tower, "Charge", chargeTime)
			end

			local head = tower:FindFirstChild("Head")
			if head and head:FindFirstChild("ChargeSound") then
				head.ChargeSound.PlaybackSpeed = 1 / chargeTime
				head.ChargeSound:Play()
			end

			while charging.Value < chargeTime do
				if not tower or not tower.Parent then break end
				charging.Value = charging.Value + 0.1
				task.wait(0.1)
			end

			isCharging.Value = false
			charging.Value = 0
			charged.Value = true
			return -- Wait until next tick to shoot
		end

		-- If charged, shoot and remain charged until all enemies are gone
		attackFunc(target, ...)
		-- Do NOT clear charged here; only clear on NO_TARGET (when all enemies are gone)
	end
end