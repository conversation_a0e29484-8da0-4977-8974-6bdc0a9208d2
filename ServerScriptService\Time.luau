local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local wave = workspace.Info.Wave
local LastWave = workspace.Info.LastWave
local info = workspace.Info

local minutes = info.Min
local seconds = info.Sec
local StartSeconds = info.StartSeconds

local WaveEnded = info:WaitForChild("WaveEnded")
local timeValue = info.Time

wave.Changed:Connect(function()
	WaveEnded.Value = false
	minutes.Value = LastWave.Value and 800 or 1  -- Set minutes based on LastWave condition
	seconds.Value = 20 + wave.Value
	StartSeconds.Value = seconds.Value

	repeat
		task.wait(1)  -- Wait for 1 second

		if WaveEnded.Value == true then
			break
		end

		seconds.Value -= 1

		-- Handle minutes and seconds countdown
		if seconds.Value < 0 then
			minutes.Value -= 1
			seconds.Value = 59
		end
		if seconds.Value > 60 then
			minutes.Value += 1
			seconds.Value = 59
		end

		-- Update the timeValue display
		if minutes.Value < 10 and seconds.Value < 10 then
			timeValue.Value = "0" .. minutes.Value .. ":0" .. seconds.Value
		else
			timeValue.Value = minutes.Value .. ":" .. (seconds.Value < 10 and "0" .. seconds.Value or seconds.Value)
		end

	until (minutes.Value <= 0 and seconds.Value <= 0) or WaveEnded.Value == true

	-- Logic after the timer ends can be placed here, if needed
end)
