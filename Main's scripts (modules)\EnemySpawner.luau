-- EnemySpawner.luau
-- Place this script inside an enemy model to spawn another enemy after a cooldown

local enemyModel = script.Parent
local ServerStorage = game:GetService("ServerStorage")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")

-- CONFIGURABLE:
local ENEMY_NAME = "EnemyNameToSpawn" -- Change to the name of the enemy to spawn (must exist in ServerStorage.Enemies)
local COOLDOWN = enemyModel:FindFirstChild("Config") and enemyModel.Config:FindFirstChild("Cooldown") and enemyModel.Config.Cooldown.Value or 5 -- fallback to 5 seconds

-- Optional: PathIndex, Map, etc. can be customized as needed
local function spawnEnemy()
	local enemyTemplate = ServerStorage:FindFirstChild("Enemies") and ServerStorage.Enemies:FindFirstChild(ENEMY_NAME)
	if not enemyTemplate then
		warn("Enemy template not found: " .. ENEMY_NAME)
		return
	end
	local newEnemy = enemyTemplate:Clone()
	if enemyModel:FindFirstChild("HumanoidRootPart") then
		newEnemy.HumanoidRootPart.CFrame = enemyModel.HumanoidRootPart.CFrame
	else
		newEnemy.Parent = Workspace.Enemy
		return
	end
	newEnemy.Parent = Workspace.Enemy
end

-- Main logic: wait for cooldown, then spawn
spawn(function()
	while enemyModel.Parent do
		task.wait(COOLDOWN)
		spawnEnemy()
	end
end)
