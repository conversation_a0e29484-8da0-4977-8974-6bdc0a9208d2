return function(attackFunc, tower, config)
	-- Ensure config values exist
	if not config:Find<PERSON>irstChild("Explosion") then
		local explosion = Instance.new("BoolValue")
		explosion.Name = "Explosion"
		explosion.Value = true
		explosion.Parent = config
	end
	if not config:FindFirstChild("ExplosionRange") then
		local explosionRange = Instance.new("NumberValue")
		explosionRange.Name = "ExplosionRange"
		explosionRange.Value = 6
		explosionRange.Parent = config
	end

	local Players = game:GetService("Players")
	local ReplicatedStorage = game:GetService("ReplicatedStorage")
	local TowerModule = require(game.ServerScriptService.Main.Tower)
	local ApplyTowerDamage = TowerModule.ApplyTowerDamage
	local EffectManager = require(script.Parent.Parent.EffectsManager)

	return function(target, ...)
		if target and target ~= "NO_TARGET" then
			-- Explosion logic: damage nearby enemies (excluding the main target)
			local explosionRange = math.ceil(config:<PERSON><PERSON>irstChild("ExplosionRange").Value)
			local mainPos = target:FindFirstChild("HumanoidRootPart") and target.HumanoidRootPart.Position or target.PrimaryPart.Position

			-- Visualize explosion (using a neon part)
			local explosionPart = Instance.new("Part")
			explosionPart.Shape = Enum.PartType.Ball
			explosionPart.Anchored = true
			explosionPart.CanCollide = false
			explosionPart.Transparency = 0.6
			explosionPart.Color = Color3.fromRGB(255, 170, 0)
			explosionPart.Material = Enum.Material.Neon
			explosionPart.Size = Vector3.new(0.5, 0.5, 0.5)
			explosionPart.CFrame = CFrame.new(mainPos)
			explosionPart.Parent = workspace
			local tweenService = game:GetService("TweenService")
			local tweenInfo = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
			local goal = {Size = Vector3.new(explosionRange*2, explosionRange*2, explosionRange*2), Transparency = 1}
			tweenService:Create(explosionPart, tweenInfo, goal):Play()
			game:GetService("Debris"):AddItem(explosionPart, 0.4)

			local ownerName = config:FindFirstChild("Owner") and config.Owner.Value or nil
			local player = ownerName and Players:FindFirstChild(ownerName) or nil
			local baseDamage = config:FindFirstChild("Damage") and config.Damage.Value or 1

			for _, enemy in ipairs(workspace.Enemy:GetChildren()) do
				if enemy ~= target and enemy:FindFirstChild("HumanoidRootPart") and enemy:FindFirstChild("Humanoid") then
					local dist = (enemy.HumanoidRootPart.Position - mainPos).Magnitude
					if dist <= explosionRange then
						-- Damage decreases with distance (linear falloff, rounded up, min 1)
						local falloff = 1 - (dist / explosionRange)
						local damage = math.max(1, math.ceil(baseDamage * falloff))
						-- Temporarily override config.Damage for this call
						local oldDamage = config.Damage.Value
						config.Damage.Value = damage
						ApplyTowerDamage(enemy, config, player)
						config.Damage.Value = oldDamage
						-- Apply effects to explosion-affected enemies
						EffectManager.ApplyEffects(tower, enemy)
					end
				end
			end

			-- Call the original attack (deals damage to the main target)
			attackFunc(target, ...)
		end
	end
end
