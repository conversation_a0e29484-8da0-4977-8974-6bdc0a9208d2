-- SafeEnemyWaypointHandler.luau
-- Enemy script: safely handles numeric waypoint logic and click/vanish behavior for special enemies
-- Place this as a Script (not ModuleScript) inside the enemy model

local enemy = script.Parent
local map = workspace:FindFirstChild("Map")
local mapFolder = map and map:FindFirstChildOfClass("Folder")
local waypoints = mapFolder and mapFolder:FindFirstChild("Waypoints")
local humanoid = enemy:FindFirstChildOfClass("Humanoid")
local torso = enemy:FindFirstChild("Torso")
local clickDetector = torso and torso:FindFirstChildOfClass("ClickDetector")
local dialogueGui = torso and torso:FindFirstChild("DialogueGui")
local movingTo = enemy:Find<PERSON>irstChild("MovingTo") or enemy:WaitForChild("MovingTo", 2)

if not (humanoid and waypoints and torso and clickDetector and movingTo) then
	warn("Missing required components for mysterious entity script.")
	return
end

-- Utility: safely get sorted numeric waypoints (returns only BaseParts with numeric names, sorted numerically)
local function getNumericWaypoints(folder)
	local list = {}
	for _, child in ipairs(folder:GetChildren()) do
		if child:Is<PERSON>("BasePart") then
			local n = tonumber(child.Name)
			if n then
				table.insert(list, child)
			end
		end
	end
	table.sort(list, function(a, b)
		return tonumber(a.Name) < tonumber(b.Name)
	end)
	return list
end

-- Find the middle waypoint (using only numeric-named waypoints)
local waypointList = getNumericWaypoints(waypoints)
if #waypointList == 0 then
	warn("No numeric waypoints found for mysterious entity script.")
	return
end
local middleIndex = math.ceil(#waypointList / 2)
local middleWaypoint = waypointList[middleIndex]
local middleWaypointNumber = tonumber(middleWaypoint.Name)

-- Stop at the middle waypoint
-- (prevents nil comparison errors and ensures robust behavior)
task.spawn(function()
	while true do
		task.wait(0.1)
		if movingTo.Value == middleWaypointNumber then
			humanoid.WalkSpeed = 0
			if enemy:FindFirstChild("HumanoidRootPart") then
				enemy.HumanoidRootPart.Anchored = true
			end
			break
		end
	end
end)

-- Click logic
local clicked = false
clickDetector.MouseClick:Connect(function()
	if clicked then return end
	enemy.Humanoid.WalkSpeed = 0
	clicked = true

	-- Give every player 350 cash (only if game.Players exists)
	local playersService = game:FindFirstChild("Players")
	if playersService and playersService:GetPlayers() then
		for _, player in ipairs(playersService:GetPlayers()) do
			if player:FindFirstChild("Gold") then
				player.Gold.Value = player.Gold.Value + 350
			end
		end
	end

	-- Show dialogue if available
	if dialogueGui then
		dialogueGui.Enabled = true
	end

	-- Slowly vanish
	for _ = 1, 20 do
		for _, part in ipairs(enemy:GetDescendants()) do
			if part:IsA("BasePart") then
				part.Transparency = math.min(1, part.Transparency + 0.05)
			end
		end
		task.wait(0.1)
	end
	enemy:Destroy()
end)

-- (No module export: this is a Script, not a ModuleScript)