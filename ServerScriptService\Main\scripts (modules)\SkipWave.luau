local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local events = ReplicatedStorage:WaitForChild("Events")
local SkipVoteEvent = events:WaitForChild("SkipVoteEvent")
local showSkip = events:WaitForChild("ShowSkipEvent")

local skip = ReplicatedStorage:WaitForChild("Skip")
local info = workspace:WaitForChild("Info")
local LastWave = info:WaitForChild("LastWave")
local SkipAllowed = info:WaitForChild("SkipAllowed")
local WaveEnded = info:WaitForChild("WaveEnded") -- New value to track if wave ended

local votedPlayers = {}
local skipGuiShown = {} -- Now a table per player

local function resetVotes()
	skip.Skip.Value = 0
	skip.Pass.Value = 0
	votedPlayers = {}
	skipGuiShown = {} -- Reset per wave
end

local function showSkipToEligible()
	for _, plr in ipairs(Players:GetPlayers()) do
		if not votedPlayers[plr.UserId] and not skipGuiShown[plr.UserId] then
			skipGuiShown[plr.UserId] = true
			showSkip:FireClient(plr, true)
		end
	end
end

local function hideSkipForAll()
	for _, plr in ipairs(Players:GetPlayers()) do
		showSkip:FireClient(plr, false)
	end
end

-- Function to handle ending the wave
local function endWave()
	WaveEnded.Value = true
	info.Sec.Value = 0
	info.Min.Value = 0
	SkipAllowed.Value = false
	hideSkipForAll()
	resetVotes()
end

info.Sec.Changed:Connect(function(changed)
	task.wait(1)
	if changed == math.round(info.StartSeconds.Value / 2) and info.Min.Value <= 1 and LastWave.Value == false and SkipAllowed.Value == false then
		SkipAllowed.Value = true
		showSkipToEligible()
	elseif changed == 1 and (info.Min.Value == 0 or LastWave.Value == true or #workspace.Enemy:GetChildren() <= 0) then
		SkipAllowed.Value = false
		hideSkipForAll()
		resetVotes()
	end
end)

SkipVoteEvent.OnServerEvent:Connect(function(player, button)
	if votedPlayers[player.UserId] then return end -- already voted this wave
	votedPlayers[player.UserId] = true
	showSkip:FireClient(player, false) -- hide skip for this player

	if button == "Skip" then
		if skip.Pass.Value >= 1 then
			skip.Pass.Value -= 1
		end
		skip.Skip.Value += 1
	elseif button == "Pass" then
		if skip.Skip.Value >= 1 then 
			skip.Skip.Value -= 1
		end
		skip.Pass.Value += 1
	end

	-- Check for voting conditions
	local playerCount = #Players:GetPlayers()
	if skip.Skip.Value >= playerCount then
		WaveEnded.Value = true
		resetVotes()
		endWave()
	elseif skip.Pass.Value >= playerCount then
		SkipAllowed.Value = false
		resetVotes()
		hideSkipForAll()
	end
end)
