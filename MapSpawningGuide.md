# Map-Based Enemy Spawning Guide

## Overview
Your Enemy module now supports maps that spawn their own enemies over time, adding strategic depth to your tower defense game.

## Map Structure Setup

### 1. Create Spawn Points in Your Map
In your Industrial Factory map, create these Parts:

```
YourMap/
├── Waypoints/ (existing)
├── Start (existing)
├── Base (existing)
├── ConveyorSpawn1 (new Part)
├── EmergencySpawn (new Part)
├── MaintenanceSpawn (new Part)
├── LoadingSpawn (new Part)
└── BossSpawn (new Part)
```

### 2. Position Spawn Points Strategically
- **ConveyorSpawn1**: Near a conveyor belt, spawns enemies mid-path
- **EmergencySpawn**: At an emergency exit, spawns from the beginning
- **MaintenanceSpawn**: In a maintenance area, spawns near the end
- **LoadingSpawn**: At a loading dock, spawns with armor
- **BossSpawn**: Hidden area for boss enemies

## Spawn Conditions

### Timer-Based Spawning
```lua
spawnCondition = "timer",
interval = 15, -- Spawn every 15 seconds
```

### Wave-Based Spawning
```lua
spawnCondition = "wave",
startWave = 5, -- Start spawning from wave 5
endWave = 15,  -- Stop after wave 15
```

### Enemy Count-Based Spawning
```lua
spawnCondition = "enemyCount",
maxEnemies = 5, -- Only spawn if ≤5 enemies on map
```

## Industrial Factory Theme Ideas

### Conveyor Belt Spawning
- Enemies appear on moving conveyor belts
- Start from middle waypoints (simulating factory workers)
- Spawn basic enemies regularly

### Emergency Exits
- Activate during higher waves
- Spawn faster enemies (panicked evacuees)
- Multiple enemies at once

### Maintenance Tunnels
- Spawn tougher enemies (maintenance workers with tools)
- Only when enemy count is low
- Start near the end of the path

### Loading Docks
- Spawn armored enemies (workers with protective gear)
- Less frequent but stronger
- Start from early waypoints

### Factory Breakdowns
- Triggered by specific waves
- Spawn boss enemies from hidden areas
- One-time events

## Advanced Features

### Dynamic Spawn Points
```lua
-- Add new spawn points during gameplay
Enemies.AddMapSpawnPoint(map, newSpawnConfig)
```

### Conditional Spawning
```lua
-- Only spawn if certain conditions are met
if currentWave >= 10 and enemyCount < 3 then
    -- Enable special spawn point
end
```

### Modifier Applications
```lua
modifiers = {
    Fortified = true,      -- Double health
    Fleetfooted = true,    -- Double speed
    DefensePercent = 25,   -- 25% damage reduction
    IsHidden = true,       -- Requires detection
    NoMoney = true         -- Gives no money when killed
}
```

## Implementation Steps

1. **Create the map structure** with spawn point Parts
2. **Place the IndustrialFactoryMapSetup.luau** script in ServerScriptService
3. **Adjust spawn point positions** and names to match your map
4. **Test different spawn conditions** and intervals
5. **Balance enemy types and modifiers** for your difficulty

## Strategic Implications

### For Players
- Must defend multiple entry points
- Resource management becomes more important
- Tower placement requires more strategy
- Creates dynamic, unpredictable gameplay

### For Map Design
- Spawn points create natural chokepoints
- Multiple paths increase complexity
- Timing creates rhythm and pacing
- Modifiers add variety to enemy types

## Example Scenarios

### Early Game (Waves 1-5)
- Only conveyor belt spawning active
- Basic enemies, low frequency
- Players learn the map layout

### Mid Game (Waves 6-15)
- Emergency exits activate
- Maintenance tunnels start spawning
- Multiple spawn points create pressure

### Late Game (Waves 16+)
- All spawn points active
- Boss spawning from hidden areas
- Maximum strategic complexity

This system transforms static maps into dynamic, living environments that actively participate in the tower defense experience!
