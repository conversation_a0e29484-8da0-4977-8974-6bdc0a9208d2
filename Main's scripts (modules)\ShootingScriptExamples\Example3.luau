local enemy = script.Parent
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local events = ReplicatedStorage:WaitForChild("Events")
local animateEnemyEvent = events:WaitForChild("AnimateEnemy")

local TweenService = game:GetService("TweenService")
local Players = game:GetService("Players")
local debris = game:GetService("Debris")

local NormalSpeed = enemy.Config.NormalSpeed
NormalSpeed.Value = enemy.Humanoid.WalkSpeed

local attackCooldown = enemy.Config.Cooldown.Value
local canAttack = false  -- Start with attack disabled
local range = 50 -- The range to detect players
local bulletDamageMin = 2
local bulletDamageMax = 3

-- Function to initialize attack delay
local function InitializeAttackDelay()
	-- Random delay between 2-4 seconds
	local initialDelay = math.random(2, 4)

	-- Visual indicator that enemy is preparing (optional)
	local prepareEffect = Instance.new("ParticleEmitter")
	prepareEffect.Color = ColorSequence.new(Color3.fromRGB(255, 165, 0))
	prepareEffect.Size = NumberSequence.new(0.5)
	prepareEffect.Transparency = NumberSequence.new({
		NumberSequenceKeypoint.new(0, 0.5),
		NumberSequenceKeypoint.new(1, 1)
	})
	prepareEffect.Lifetime = NumberRange.new(0.5, 1)
	prepareEffect.Rate = 10
	prepareEffect.Speed = NumberRange.new(1, 2)
	prepareEffect.Parent = enemy.Head

	-- Wait for the initial delay
	task.wait(initialDelay)

	-- Clean up effect and enable attacks
	prepareEffect:Destroy()
	canAttack = true

	-- Optional: Play a "ready" sound or animation
	if enemy.Head:FindFirstChild("Ready") then
		enemy.Head.Ready:Play()
	end
end

local function DetectPlayerInRange()
	local enemyPosition = enemy.PrimaryPart.Position
	local playersInRange = {}

	for _, player in ipairs(Players:GetPlayers()) do
		local character = player.Character
		if character and character:FindFirstChild("HumanoidRootPart") and character:FindFirstChild("Humanoid") and character.Humanoid.Health > 0 then
			local distance = (character.HumanoidRootPart.Position - enemyPosition).Magnitude
			if distance <= range then
				table.insert(playersInRange, character)
			end
		end
	end

	if #playersInRange > 0 then
		return playersInRange[math.random(1, #playersInRange)] -- Return a random player in range
	end
	return nil
end

local function FireBullet(playerCharacter)
	local targetPart = playerCharacter:FindFirstChild("HumanoidRootPart")
	if not targetPart then return end

	-- Adjust the enemy's rotation to face the player before shooting
	local targetCFrame = CFrame.lookAt(enemy.PrimaryPart.Position, Vector3.new(targetPart.Position.X, enemy.PrimaryPart.Position.Y, targetPart.Position.Z))
	enemy.PrimaryPart.CFrame = targetCFrame

	-- Create bullet
	local bullet = Instance.new("Part")
	bullet.Size = Vector3.new(0.2, 0.2, 0.2)
	bullet.Anchored = false
	bullet.CanCollide = false
	bullet.Color = Color3.new(1, 1, 0)
	bullet.Material = Enum.Material.Neon
	bullet.BrickColor = BrickColor.new("Bright yellow")
	bullet.Parent = workspace

	-- Create attachments for the trail
	local attachment0 = Instance.new("Attachment", bullet)
	local attachment1 = Instance.new("Attachment", bullet)

	-- Offset attachment0 slightly forward (along Z-axis)
	attachment0.Position = Vector3.new(0, 0.1, 0.1) -- Slightly offset forward
	enemy.Head:FindFirstChild("Shoot"):Play()

	-- Create trail
	local trail = Instance.new("Trail")
	trail.Attachment0 = attachment0 -- Attach the trail to the bullet using attachments
	trail.Attachment1 = attachment1
	trail.Color = ColorSequence.new(Color3.new(1, 1, 0), Color3.new(1, 0.5, 0)) -- Yellow to orange gradient
	trail.Lifetime = 0.5 -- Time for the trail to fade away
	trail.Transparency = NumberSequence.new(0, 1) -- Fades from fully visible to invisible
	trail.LightEmission = 1 -- Makes the trail glow
	trail.Parent = bullet

	-- Position and orient bullet towards the player
	local direction = (targetPart.Position - enemy.PrimaryPart.Position).Unit
	bullet.CFrame = CFrame.new(enemy.PrimaryPart.Position, targetPart.Position) -- Set initial position facing the player

	-- Fire the attack animation with each bullet
	animateEnemyEvent:FireAllClients(enemy, "AttackAnim")

	-- Bullet movement
	local bulletSpeed = 100
	local bulletVelocity = Instance.new("BodyVelocity")
	bulletVelocity.MaxForce = Vector3.new(1, 1, 1) * math.huge
	bulletVelocity.Velocity = direction * bulletSpeed
	bulletVelocity.Parent = bullet

	-- Damage player when hit
	bullet.Touched:Connect(function(hit)
		local humanoid = hit.Parent:FindFirstChild("Humanoid")
		if humanoid and humanoid.Parent == playerCharacter then
			humanoid:TakeDamage(math.random(bulletDamageMin, bulletDamageMax))
			bullet:Destroy()
		end
	end)

	-- Auto-destroy bullet and fade the trail after 5 seconds
	debris:AddItem(bullet, 4)
end

local function AttackPlayer(playerCharacter)
	enemy.Humanoid.WalkSpeed = 0

	-- Shoot multiple bullets at the player
	for i = 1, math.random(10, 20) do
		FireBullet(playerCharacter)
		task.wait(0.1) -- Delay between bullets
	end

	enemy.Humanoid.WalkSpeed = NormalSpeed.Value
end

-- Initialize the attack delay when the script starts
task.spawn(InitializeAttackDelay)

while enemy do
	local targetPlayer = DetectPlayerInRange()
	if targetPlayer and canAttack then
		AttackPlayer(targetPlayer)
		canAttack = false
		task.wait(attackCooldown)
		canAttack = true
	end
	task.wait(1)
end