local ServerStorage = game:GetService("ServerStorage")
local PhysicsService = game:GetService("PhysicsService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local events = ReplicatedStorage:WaitForChild("Events")


local spawnRequestEvent = ReplicatedStorage:WaitForChild("Events"):WaitFor<PERSON>hild("RequestUnitSpawn")

local animateUnitEvent = events:WaitForChild("AnimateUnit")

local TowerFunctions = require(script.Parent:WaitForChild("TowerFunctions"))

-- Helper to handle all cash logic for a unit damaging/killing an enemy, just like towers
local function HandleUnitCash(enemy, player)
	TowerFunctions.HandleRepeatMoney(enemy, player)
	local noMoney = enemy.Config:FindFirstChild("NoMoney")
	if not noMoney or noMoney.Value == false then
		if enemy.Humanoid.Health <= 0 then
			local cash = TowerFunctions.GetKillCash(enemy)
			TowerFunctions.SendCash(player, cash)
		end
	end
end

local Unit = {}

function Unit.FindTarget(newUnit)
	local DanDistance = newUnit.Config:FindFirstChild("Range") and newUnit.Config.Range.Value or 15
	local t = nil
	for _, target in ipairs (workspace.Enemy:GetChildren()) do
		local distance = (target.HumanoidRootPart.Position - newUnit.HumanoidRootPart.Position).Magnitude
		if distance < DanDistance then
			newUnit.Humanoid.WalkSpeed = 0
			t = target
			DanDistance = distance
		end
	end
	return t
end

-- Helper: Apply damage to shield first, then health, but treat shield as part of total durability
local function applyShieldedDamage(enemy, damage)
	local shield = enemy.Config:FindFirstChild("ShieldHealth")
	local shieldValue = shield and shield.Value or 0
	local healthValue = enemy.Humanoid.Health
	local totalDurability = shieldValue + healthValue
	if damage >= totalDurability then
		if shield then shield.Value = 0 end
		enemy.Humanoid.Health = 0
	else
		if shield and shieldValue > 0 then
			if shieldValue >= damage then
				shield.Value = shieldValue - damage
			else
				local leftover = damage - shieldValue
				shield.Value = 0
				enemy.Humanoid.Health = math.max(0, healthValue - leftover)
			end
		else
			enemy.Humanoid.Health = math.max(0, healthValue - damage)
		end
	end
end

function Unit.Attack(newUnit, player)
	local target = Unit.FindTarget(newUnit)
	local config = newUnit.Config

	if target and target:FindFirstChild("Humanoid") and target.Humanoid.Health > 0 then
		local targetCFrame = CFrame.lookAt(newUnit.Head.Position, Vector3.new(target.Head.Position.X, newUnit.Head.Position.Y, target.Head.Position.Z))
		newUnit.Head.CFrame = targetCFrame

		animateUnitEvent:FireAllClients(newUnit, "Attack", target)

		TowerFunctions.HandleRepeatMoney(target, player)
		local damage
		if not target.Config:FindFirstChild("DefensePercent") then
			damage = config.Damage.Value
		else
			damage = math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100)))
		end

		-- Shield logic
		applyShieldedDamage(target, damage)

		if target.Humanoid.Health <= 0 then
			local noMoney = target.Config:FindFirstChild("NoMoney")
			if not noMoney or noMoney.Value == false then
				player.Kills.Value = (player.Kills.Value or 0) + 1
				local cash = TowerFunctions.GetKillCash(target)
				TowerFunctions.SendCash(player, cash)
			end
		end

		if newUnit.Head:FindFirstChild("Shoot") then
			newUnit.Head.Shoot:Play()
		end
		task.wait(config.Cooldown.Value)
	elseif not target or target.Humanoid.Health == 0 then
		newUnit.Humanoid.WalkSpeed = newUnit.Config.NormalSpeed.Value or 5
	end
	task.wait()
	if newUnit and newUnit.Parent then
		task.spawn(function()
			Unit.Attack(newUnit, player)
		end)
	end
end




function Unit.Optimize(UnitToOptimize)
	local humanoid = UnitToOptimize:FindFirstChild("Humanoid")

	if UnitToOptimize:FindFirstChild("HumanoidRootPart") then 
		UnitToOptimize.HumanoidRootPart:SetNetworkOwner(nil) 
	elseif UnitToOptimize.PrimaryPart ~= nil then
		UnitToOptimize.PrimaryPart:SetNetworkOwner(nil)
	end
	if not humanoid then return end
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Seated, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Running, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Climbing, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Landed, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
end

local RunService = game:GetService("RunService")

-- Table to track all moving units
local ActiveUnits = {}

-- Grid-based spatial partitioning settings
local GRID_SIZE = 10 -- studs per cell
local function getCell(pos)
	return math.floor(pos.X / GRID_SIZE), math.floor(pos.Z / GRID_SIZE)
end

local function getNearbyCells(cellX, cellZ)
	local cells = {}
	for dx = -1, 1 do
		for dz = -1, 1 do
			table.insert(cells, {cellX + dx, cellZ + dz})
		end
	end
	return cells
end

-- Shared update loop for all units (with grid-based collision)
RunService.Heartbeat:Connect(function(dt)
	-- Build enemy grid
	local enemyGrid = {}
	for _, enemy in ipairs(workspace.Enemy:GetChildren()) do
		if enemy:FindFirstChild("HumanoidRootPart") and enemy:FindFirstChild("Humanoid") and enemy.Humanoid.Health > 0 then
			local pos = enemy.HumanoidRootPart.Position
			local cx, cz = getCell(pos)
			enemyGrid[cx] = enemyGrid[cx] or {}
			enemyGrid[cx][cz] = enemyGrid[cx][cz] or {}
			table.insert(enemyGrid[cx][cz], enemy)
		end
	end

	for i = #ActiveUnits, 1, -1 do
		local data = ActiveUnits[i]
		local unit = data.unit
		if not unit or not unit.Parent or unit.Humanoid.Health <= 0 then
			table.remove(ActiveUnits, i)
			if unit and unit.Parent then unit:Destroy() end
			continue
		end

		local hrp = unit:FindFirstChild("HumanoidRootPart") or unit.PrimaryPart
		if not hrp then continue end

		local targetWaypoint = data.waypoints[data.current]
		if not targetWaypoint then
			table.remove(ActiveUnits, i)
			unit:Destroy()
			continue
		end

		local targetPosition = targetWaypoint.Position + data.offset
		if (hrp.Position - targetPosition).Magnitude > 0.5 then
			local direction = (targetPosition - hrp.Position).Unit
			local step = unit.Humanoid.WalkSpeed * dt
			local hrppos = hrp.Position + direction * step
			hrppos = Vector3.new(hrppos.X, targetPosition.Y, hrppos.Z)
			if unit.Humanoid.WalkSpeed > 0 then
				local currentLook = hrp.CFrame.LookVector
				local lerpAlpha = 0.15
				local newLook = currentLook:Lerp(direction, lerpAlpha).Unit
				local newCFrame = CFrame.new(hrppos, hrppos + newLook)
				hrp.CFrame = newCFrame
			else
				-- If not moving, only update position, do not change facing
				hrp.Position = hrppos
			end
			-- Grid-based collision check (every 0.1s)
			data.collisionTimer = (data.collisionTimer or 0) + dt
			data.collisionCooldown = (data.collisionCooldown or 0) - dt
			if data.collisionTimer > 0.1 and (not data.collisionCooldown or data.collisionCooldown <= 0) then
				data.collisionTimer = 0
				local upos = hrp.Position
				local ucx, ucz = getCell(upos)
				local collided = false
				for _, cell in ipairs(getNearbyCells(ucx, ucz)) do
					local cx, cz = cell[1], cell[2]
					if enemyGrid[cx] and enemyGrid[cx][cz] then
						for _, enemy in ipairs(enemyGrid[cx][cz]) do
							local dist = (enemy.HumanoidRootPart.Position - hrp.Position).Magnitude
							if dist < 3 then
								local humanoid = unit.Humanoid
								local unitHealth = humanoid.Health
								local enemyHealth = enemy.Humanoid.Health
								local unitOwner = unit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
								local player = game.Players:FindFirstChild(unitOwner and unitOwner.Value or "")
								if enemyHealth > unitHealth then
									humanoid.Health = 0
									if unitOwner and unitOwner.Value then
										TowerFunctions.HandleRepeatMoney(enemy, player)
										if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
											local cash = TowerFunctions.GetKillCash(enemy)
											TowerFunctions.SendCash(player, cash)
										end
									end
									applyShieldedDamage(enemy, unitHealth)
								elseif unitHealth > enemyHealth then
									enemy.Humanoid.Health = 0
									if unitOwner and unitOwner.Value then
										TowerFunctions.HandleRepeatMoney(enemy, player)
										if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
											local cash = TowerFunctions.GetKillCash(enemy)
											TowerFunctions.SendCash(player, cash)
										end
									end
									humanoid.Health = humanoid.Health - enemyHealth
									applyShieldedDamage(enemy, enemyHealth)
								else
									if unitOwner and unitOwner.Value then
										TowerFunctions.HandleRepeatMoney(enemy, player)
										if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
											local cash = TowerFunctions.GetKillCash(enemy)
											TowerFunctions.SendCash(player, cash)
										end
									end
									humanoid.Health = 0
									applyShieldedDamage(enemy, unitHealth)
								end
								collided = true
								break
							end
						end
						if collided then break end
					end
					if collided then break end
				end
				if collided then
					data.collisionCooldown = 0.2 -- 0.2s cooldown after a collision
				end
			end
		else
			data.current = data.current - 1
			-- Update MovingTo.Value to match current waypoint
			local movingTo = unit:FindFirstChild("MovingTo")
			if movingTo and typeof(movingTo) == "Instance" and movingTo:IsA("IntValue") then
				movingTo.Value = data.current
			end
			if data.current < 1 then
				table.remove(ActiveUnits, i)
				unit:Destroy()
			end
		end
	end
end)

-- Helper: Get the correct path folder for a unit, supporting multiple paths
local function getUnitPathFolder(map, unit)
	if not map or not map:FindFirstChild("Waypoints") or not unit then
		return nil
	end
	local waypointsFolder = map.Waypoints
	-- Try to get PathIndex from Attribute or IntValue
	local pathIndex = unit:GetAttribute("PathIndex")
	if not pathIndex then
		local pathIndexValue = unit:FindFirstChild("PathIndex")
		if pathIndexValue and pathIndexValue:IsA("IntValue") then
			pathIndex = pathIndexValue.Value
		end
	end
	-- Gather all numbered Path folders (Path1, Path2, ...), fallback to any Path
	local pathFolders = {}
	for _, child in ipairs(waypointsFolder:GetChildren()) do
		if child:IsA("Folder") and child.Name:match("^Path%d+$") then
			table.insert(pathFolders, child)
		end
	end
	if #pathFolders == 0 then
		for _, child in ipairs(waypointsFolder:GetChildren()) do
			if child:IsA("Folder") and child.Name:lower():match("^path$") then
				table.insert(pathFolders, child)
			end
		end
	end
	if #pathFolders == 0 then
		return waypointsFolder -- fallback: no path folders, use direct children
	end
	-- Sort pathFolders by number (Path1, Path2, ...)
	table.sort(pathFolders, function(a, b)
		local na = tonumber(a.Name:match("%d+$")) or 0
		local nb = tonumber(b.Name:match("%d+$")) or 0
		return na < nb
	end)
	local useIndex = pathIndex or 1
	if useIndex < 1 or useIndex > #pathFolders then useIndex = 1 end
	return pathFolders[useIndex]
end

-- Helper: Get all path folders for a map
local function getUnitPaths(map)
	local paths = {}
	if not map or not map:FindFirstChild("Waypoints") then return paths end
	local waypointsFolder = map.Waypoints
	for _, child in ipairs(waypointsFolder:GetChildren()) do
		if child:IsA("Folder") and child.Name:match("^Path%d+$") then
			table.insert(paths, child)
		end
	end
	if #paths == 0 then
		for _, child in ipairs(waypointsFolder:GetChildren()) do
			if child:IsA("Folder") and child.Name:lower():match("^path$") then
				table.insert(paths, child)
			end
		end
	end
	if #paths == 0 then
		table.insert(paths, waypointsFolder) -- fallback: no path folders, use direct children
	end
	-- Sort paths by number (Path1, Path2, ...)
	table.sort(paths, function(a, b)
		local na = tonumber(a.Name:match("%d+$")) or 0
		local nb = tonumber(b.Name:match("%d+$")) or 0
		return na < nb
	end)
	return paths
end

-- Module-level variable to track last used path index for round-robin
local lastUnitPathIndex = 0

function Unit.Move(newUnit, map)
	if not newUnit or not map then
		warn("Move function aborted: newUnit or map is nil.")
		return
	end

	local humanoid = newUnit:FindFirstChild("Humanoid")
	local hrp = newUnit:FindFirstChild("HumanoidRootPart") or newUnit.PrimaryPart
	local pathFolder = getUnitPathFolder(map, newUnit)
	if not humanoid or not hrp or not pathFolder then
		warn("Waypoints or HumanoidRootPart not found in map:", map.Name or "Unknown")
		return
	end

	local hrpparts = {}
	for _, child in ipairs(pathFolder:GetChildren()) do
		if child:IsA("BasePart") then
			table.insert(hrpparts, child)
		end
	end
	-- Sort waypoints numerically by name
	table.sort(hrpparts, function(a, b)
		return tonumber(a.Name) < tonumber(b.Name)
	end)

	local hrppartsCount = #hrpparts
	local offset = Vector3.new(math.random(-50, 50) / 100, 2, math.random(-50, 50) / 100)
	local startWaypoint = newUnit:FindFirstChild("MovingTo") and newUnit.MovingTo.Value or hrppartsCount

	hr = hrp
	hr.Anchored = true

	table.insert(ActiveUnits, {
		unit = newUnit,
		waypoints = hrpparts,
		current = startWaypoint,
		offset = offset,
		collisionTimer = 0
	})
end

function Unit.Spawn(name, quantity, map, unitOwnerValue, PathIndexOverride)
	local UnitExists = ServerStorage.Units:FindFirstChild(name)
	local SpawnedUnits = {}
	if UnitExists then
		local paths = getUnitPaths(map)
		local numPaths = #paths
		for i = 1, quantity do
			task.wait(0.5)
			local RegisteredMap = map
			local newUnit = UnitExists:Clone()
			-- Determine path index for this unit
			local pathIndex = PathIndexOverride
			if not pathIndex then
				lastUnitPathIndex = ((lastUnitPathIndex) % numPaths) + 1
				pathIndex = lastUnitPathIndex
			end
			-- Use last waypoint of the correct path as spawn point
			local pathFolder = paths[pathIndex] or paths[1]
			local waypoints = {}
			for _, child in ipairs(pathFolder:GetChildren()) do
				if child:IsA("BasePart") then
					table.insert(waypoints, child)
				end
			end
			table.sort(waypoints, function(a, b)
				return tonumber(a.Name) < tonumber(b.Name)
			end)
			local lastWaypoint = waypoints[#waypoints]
			if lastWaypoint then
				newUnit.HumanoidRootPart.CFrame = lastWaypoint.CFrame
			else
				newUnit.HumanoidRootPart.CFrame = RegisteredMap.Base.Head.CFrame
			end
			newUnit.Parent = workspace.Unit
			Unit.Optimize(newUnit)

			local movingTo = Instance.new("IntValue")
			movingTo.Name = "MovingTo"
			movingTo.Value = #waypoints
			movingTo.Parent = newUnit

			-- Set PathIndex attribute and IntValue for robust persistence
			newUnit:SetAttribute("PathIndex", pathIndex)
			local pathIndexValue = Instance.new("IntValue")
			pathIndexValue.Name = "PathIndex"
			pathIndexValue.Value = pathIndex
			pathIndexValue.Parent = newUnit

			local NormalSpeed = newUnit.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
			NormalSpeed.Name = "NormalSpeed"
			NormalSpeed.Value = newUnit.Humanoid.WalkSpeed
			NormalSpeed.Parent = newUnit.Config

			-- Set the unit owner value
			local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
			if unitOwner then
				unitOwner.Value = unitOwnerValue
			end

			for _, object in ipairs(newUnit:GetDescendants()) do
				if object:IsA("BasePart") then
					object.CollisionGroup = "Unit"
				end
			end

			newUnit.HumanoidRootPart.Touched:Connect(function(hit)
				if not hit.Parent then return end

				local enemy = hit.Parent
				if enemy.Parent == workspace.Enemy and enemy:FindFirstChild("Humanoid") then
					local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
					local player = game.Players:FindFirstChild(unitOwner and unitOwner.Value or "")
					local unitHealth = newUnit.Humanoid.Health
					local shield = enemy.Config:FindFirstChild("ShieldHealth")
					if shield and shield.Value > 0 then
						shield.Value = math.max(0, shield.Value - unitHealth)
					else
						enemy.Humanoid.Health = math.max(0, enemy.Humanoid.Health - unitHealth)
					end
					if enemy.Humanoid.Health <= 0 and (not shield or shield.Value <= 0) then
						HandleUnitCash(enemy, player)
					end
					newUnit.Humanoid.Health = 0
				end
			end)

			newUnit.Humanoid.Died:Connect(function()
				task.wait(0.5)
				newUnit:Destroy()
			end)

			coroutine.wrap(Unit.Move)(newUnit, RegisteredMap)
			coroutine.wrap(Unit.Attack)(newUnit, game.Players:FindFirstChild(unitOwnerValue))
			table.insert(SpawnedUnits, newUnit)
		end
	else
		warn("Requested Unit does not exist:", name)
	end
	return SpawnedUnits
end

function Unit.Summon(name, quantity, map, cframe, MovingToVal, unitOwnerValue, PathIndexOverride)
	local UnitExists = ServerStorage.Units:FindFirstChild(name)
	local SpawnedUnits = {}
	if UnitExists then
		local paths = getUnitPaths(map)
		local numPaths = #paths
		for i = 1, quantity do
			task.wait(0.5)
			local pathIndex = PathIndexOverride
			if not pathIndex then
				lastUnitPathIndex = ((lastUnitPathIndex) % numPaths) + 1
				pathIndex = lastUnitPathIndex
			end
			local pathFolder = paths[pathIndex] or paths[1]
			local waypoints = {}
			for _, child in ipairs(pathFolder:GetChildren()) do
				if child:IsA("BasePart") then
					table.insert(waypoints, child)
				end
			end
			table.sort(waypoints, function(a, b)
				return tonumber(a.Name) < tonumber(b.Name)
			end)
			local spawnCFrame = cframe or (waypoints[#waypoints] and waypoints[#waypoints].CFrame) or map.Base.Head.CFrame
			local newUnit = UnitExists:Clone()
			newUnit.HumanoidRootPart.CFrame = spawnCFrame
			newUnit.Parent = workspace.Unit
			newUnit.HumanoidRootPart:SetNetworkOwner(nil)

			local movingTo = Instance.new("IntValue")
			movingTo.Name = "MovingTo"
			movingTo.Value = MovingToVal or #waypoints
			movingTo.Parent = newUnit

			-- Set PathIndex attribute and IntValue for robust persistence
			newUnit:SetAttribute("PathIndex", pathIndex)
			local pathIndexValue = Instance.new("IntValue")
			pathIndexValue.Name = "PathIndex"
			pathIndexValue.Value = pathIndex
			pathIndexValue.Parent = newUnit

			local NormalSpeed = newUnit.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
			NormalSpeed.Name = "NormalSpeed"
			NormalSpeed.Value = newUnit.Humanoid.WalkSpeed
			NormalSpeed.Parent = newUnit.Config

			-- Set the unit owner value
			local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
			if unitOwner then
				unitOwner.Value = unitOwnerValue
			end

			for _, object in ipairs(newUnit:GetDescendants()) do
				if object:IsA("BasePart") then
					object.CollisionGroup = "Unit"
				end
			end

			newUnit.HumanoidRootPart.Touched:Connect(function(hit)
				if not hit.Parent then return end

				local enemy = hit.Parent
				if enemy.Parent == workspace.Enemy and enemy:FindFirstChild("Humanoid") then
					local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
					local player = game.Players:FindFirstChild(unitOwner.Value)
					local unitHealth = newUnit.Humanoid.Health
					-- Always apply damage to shield first, then health, regardless of enemy's HP
					applyShieldedDamage(enemy, unitHealth)
					-- Handle cash if enemy died
					if enemy.Humanoid.Health <= 0 then
						HandleUnitCash(enemy, player)
					end
					-- Unit always dies on collision
					newUnit.Humanoid.Health = 0
				end
			end)

			newUnit.Humanoid.Died:Connect(function()
				task.wait(0.5)
				newUnit:Destroy()
			end)

			coroutine.wrap(Unit.Move)(newUnit, map)
			coroutine.wrap(Unit.Attack)(newUnit, game.Players:FindFirstChild(unitOwnerValue))
			table.insert(SpawnedUnits, newUnit)
		end
	else
		warn("Requested Unit does not exist:", name)
	end
	return SpawnedUnits
end
-- Function to handle BindableEvent spawn requests
local function onRequestSpawn(name, quantity, map, unitOwnerValue)
	Unit.Spawn(name, quantity, map, unitOwnerValue)
end

-- Connect the BindableEvent to the handler
spawnRequestEvent.Event:Connect(onRequestSpawn)


return Unit
