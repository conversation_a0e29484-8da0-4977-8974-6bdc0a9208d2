-- MaD-90 Portal Spawn Logic
-- Place this script inside MaD-90 enemy model

local mob = require(game.ServerScriptService.Main.Enemy)
local mad90 = script.Parent
local config = mad90:FindFirstChild("Config")
local map = workspace:FindFirstChild("Map") and workspace.Map:FindFirstChildOfClass("Folder")
local Cooldown = config and config:Find<PERSON>irstChild("Cooldown") and config.Cooldown.Value or 5

while mad90.Parent do
	if not map then return end
	local hrp = mad90:FindFirstChild("HumanoidRootPart") or mad90.PrimaryPart
	if not hrp then return end

	local numToSpawn = math.random(3, 6)
	for i = 1, numToSpawn do
		local angle = math.rad((i - 1) * (360 / numToSpawn))
		local radius = 0 -- No offset, spawn at MaD-90's HRP position
		local offset = Vector3.new(math.cos(angle) * radius, 0, math.sin(angle) * radius)
		local spawnPos = hrp.Position + offset
		local spawnCFrame = CFrame.new(spawnPos, hrp.Position)
		-- You can change "SpawnedEnemy" to any enemy type you want to spawn
		mob.Su<PERSON><PERSON>("Spawn3", 1, map, spawnCFrame)
		task.wait(0.2)
	end
	-- Portal effect: you can add a visual effect here if desired
	wait(Cooldown)
end
