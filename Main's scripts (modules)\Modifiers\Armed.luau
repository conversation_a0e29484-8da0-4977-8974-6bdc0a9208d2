-- Armed.luau
local Armed = {}

function Armed.Apply(enemy, gunName)
    print("[Armed] Applying Armed modifier with gun:", gunName)
    -- Call GunModule.Equip via a global or shared reference
    if _G.GunModule and type(_G.GunModule.Equip) == "function" then
        return _G.GunModule.Equip(enemy, gunName)
    else
        warn("[Armed] _G.GunModule.Equip not found!")
        return false
    end
end

return Armed
