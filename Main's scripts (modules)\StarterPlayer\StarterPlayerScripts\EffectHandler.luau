local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Events = ReplicatedStorage:WaitForChild("Events")
local effectEvent = Events:WaitForChild("PlayerEffect") -- or "GunEffect"


local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- Ensure Effects folder exists
local effectsFolder = player:FindFirstChild("Effects") or Instance.new("Folder", player)
effectsFolder.Name = "Effects"

local event = ReplicatedStorage:WaitForChild("GunEffectEvent")
local effectModules = ReplicatedStorage:WaitForChild("EffectModules")

effectEvent.OnClientEvent:Connect(function(effectName, params)
	local effectModule = effectModules:FindFirstChild(effectName)
	if not effectModule then return end

	-- Only one instance per effect
	local effectInstance = effectsFolder:FindFirstChild(effectName)
	if not effectInstance then
		effectInstance = Instance.new("Folder")
		effectInstance.Name = effectName
		effectInstance.Parent = effectsFolder
	end

	require(effectModule).Apply(effectInstance, params or {})
end)