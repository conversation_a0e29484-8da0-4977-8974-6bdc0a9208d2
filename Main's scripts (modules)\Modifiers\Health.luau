-- Health.luau (place in <PERSON>'s scripts (modules)/Modifiers/)
local module = {}

function module.Apply(enemy)
	task.spawn(function()
		local config = enemy:FindFirstChild("Config")
		local humanoid = enemy:Find<PERSON><PERSON>t<PERSON>hild("Humanoid")

		if not config then
			warn("[Debug] Config not found in enemy!")
			return
		end

		-- Try up to 20 times to find HealthValue
		local healthValue
		for attempt = 1, 20 do
			healthValue = config:FindFirstChild("HealthValue")
			if healthValue then break end
			task.wait(0.1)
		end

		if healthValue and healthValue:IsA("NumberValue") and humanoid then
			humanoid.MaxHealth = healthValue.Value
			humanoid.Health = healthValue.Value
			print("[Rahhh] Applied health to enemy: "..healthValue.Value)
		else
			warn("[Debug] Failed to find valid HealthValue after 20 attempts.")
			print("[Debug] Current contents of Config:")
			for _, child in ipairs(config:<PERSON><PERSON><PERSON><PERSON><PERSON>()) do
				print(" -", child.Name, "(", child.ClassName, ")")
			end
		end
	end)
end

return module
