-- AutoGunAssigner.luau
-- Ensures all enemies with an Armed value get their gun at game start and when added

local GunModule = require(script.Parent.Modifiers.GunModule)

local function applyGunToEnemy(enemy)
	local config = enemy:FindFirstChild("Config")
	if not config then return end
	local armed = config:FindFirstChild("Armed")
	if armed and armed:IsA("StringValue") and armed.Value ~= "" then
		GunModule.Apply(enemy, armed.Value)
	end
end

-- Apply to all existing enemies at game start
local enemyFolder = workspace:FindFirstChild("Enemy")
if enemyFolder then
	for _, enemy in ipairs(enemyFolder:GetChildren()) do
		applyGunToEnemy(enemy)
	end
	-- Listen for new enemies being added
	enemyFolder.ChildAdded:Connect(function(child)
		applyGunToEnemy(child)
		-- Listen for Armed value being added/changed
		local config = child:FindFirstChild("Config")
		if config then
			local armed = config:FindFirstChild("Armed")
			if armed then
				armed.Changed:Connect(function()
					applyGunToEnemy(child)
				end)
			end
			config.ChildAdded:Connect(function(val)
				if val.Name == "Armed" and val:IsA("StringValue") then
					val.Changed:Connect(function()
						applyGunToEnemy(child)
					end)
					applyGunToEnemy(child)
				end
			end)
		end
	end)
end
