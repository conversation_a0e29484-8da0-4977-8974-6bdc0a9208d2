local SummonEvent = game:GetService("ReplicatedStorage").Events.EnemyAbility
local AnimateEvent = game:GetService("ReplicatedStorage").Events.AnimateEnemy
local Enemies = require(game: GetService("ServerScriptService").Main.Enemy) 

local spawnedEnemies = {}

local function ModifyConfiguration(enemy1, modifiers)
	if not enemy1 or not enemy1:FindFirstChild("Config") then
		warn("Enemy or enemy configuration is missing.")
		return
	end

	for modifier, value in pairs(modifiers) do
		local configModifier = enemy1.Config:FindFirstChild(modifier)

		if configModifier then
			-- If the modifier already exists, update its value
			configModifier.Value = value
		else
			-- Create the modifier based on its type
			local newModifier
			if typeof(value) == "boolean" then
				newModifier = Instance.new("BoolValue")
			elseif typeof(value) == "number" then
				newModifier = Instance.new("NumberValue")
			else
				warn("Unsupported modifier type for:", modifier)
				continue
			end
			newModifier.Name = modifier
			newModifier.Value = value
			newModifier.Parent = enemy1.Config
		end
	end
end

local function SpawnEnemy(name, quantity, map, modifiers)
	task.spawn(function()
		for i = 1, quantity do
			local newEnemy = Enemies.Spawn(name, 1, map)[1]  -- Spawn a single enemy

			if newEnemy then
				if modifiers then
					ModifyConfiguration(newEnemy, modifiers)
				end
			end
		end
	end)
end
-- Function to spawn an enemy, applying modifiers individually
local function SummonEnemy(name, quantity, map, cframe, movingToVal, modifiers)
	for i = 1, quantity do
		local newEnemy = Enemies.Summon(name, 1, map,cframe, movingToVal)[1]  -- Spawn a single enemy

		-- If a new enemy is created and modifiers are provided, apply them
		if newEnemy and modifiers then
			ModifyConfiguration(newEnemy, modifiers)
		end

		-- Add the new enemy to the spawnedEnemies table
		table.insert(spawnedEnemies, newEnemy)
	end
end

SummonEvent.Event:Connect(function(enemy)
	local map = workspace.Map:FindFirstChildOfClass("Folder")
	if enemy.Name == "Swarmcore" and enemy then
		enemy.Humanoid.WalkSpeed = 0
		AnimateEvent:FireAllClients(enemy, "SummonAnim")
		task.wait(1.7)
		SummonEnemy("Botling",math.random(2,3),map,enemy.PrimaryPart.CFrame,enemy.MovingTo.Value)
		wait(1.9)
		enemy.Humanoid.WalkSpeed = enemy.Config.NormalSpeed.Value
	end
	if enemy.Name == "Arcane sentinel" and enemy then
		AnimateEvent:FireAllClients(enemy, "SummonAnim")
		task.wait(0.5)
		SummonEnemy("Arcanos servant",math.random(3,4),map,enemy.PrimaryPart.CFrame,enemy.MovingTo.Value)

	end
	if enemy.Name == "Eclipse Enforcer" and enemy then
		SpawnEnemy("Raider", 1, map, {IsHidden = true})
		task.wait(2)
		SpawnEnemy("Armed", 5, map)
		task.wait(4)
		SpawnEnemy("Prepared", 8, map)
		SpawnEnemy("Champion", 1, map)
		SpawnEnemy("Credulous", 8, map,{DefensePercent = 80})
	end
	if enemy.Name == "Arcanos Prime" and enemy then
		SpawnEnemy("Sentinel", 1, map, {IsHidden = true})
		task.wait(2)
		SpawnEnemy("Raider", 5, map)
		task.wait(1)
		SpawnEnemy("Bomber", 1, map)
		task.wait(4)
		SpawnEnemy("Arcanos servant",6,map)
		SpawnEnemy("Armored", 8, map,{DefensePercent = 80})
	end
end)
