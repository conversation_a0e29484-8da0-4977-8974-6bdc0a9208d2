local ServerStorage = game:GetService("ServerStorage")
local PhysicsService = game:GetService("PhysicsService")
local ContentProvider = game:GetService("ContentProvider")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Add this line to require the ModifierManager
local ModifierManager = require(script.Parent.ModifierManager)

local mob = {}

-- Store death spawn configurations for enemies
local deathSpawnConfigs = {}

-- Define a list of internal values that shouldn't be displayed as modifiers
local internalValues = {
	["OriginalMaxHealth"] = true,
	["NormalSpeed"] = true,
	["MovingTo"] = true,
	["Owner"] = true,
	["UnitOwner"] = true,
	["Cooldown"] = true,
	["NoMoney"] = true,
	["Spent"] = true
}

-- Replicate the internal values to clients
local function replicateInternalValues()
	local hiddenValues = ReplicatedStorage:FindFirstChild("HiddenModifiers") or Instance.new("Folder")
	hiddenValues.Name = "HiddenModifiers"
	hiddenValues.Parent = ReplicatedStorage

	-- Clear existing values
	for _, child in pairs(hiddenValues:GetChildren()) do
		child:Destroy()
	end

	-- Add each internal value
	for name, _ in pairs(internalValues) do
		local value = Instance.new("StringValue")
		value.Name = name
		value.Value = "hidden"
		value.Parent = hiddenValues
	end
end

-- Call this function to ensure clients have the latest list
replicateInternalValues()

-- Function to modify enemy configuration
local function modifyConfiguration(enemy, modifiers, intendedCFrame)
	if not enemy or not enemy:FindFirstChild("Config") then
		warn("Enemy or enemy configuration is missing.")
		return
	end

	-- Apply modifiers using the ModifierManager, passing intendedCFrame for gun logic
	ModifierManager.ApplyModifiers(enemy, modifiers, intendedCFrame)
	-- Ensure string-based modifiers (like Armed) are watched for mid-game changes
	ModifierManager.WatchStringModifiers(enemy)
end

-- Function to handle a spawned enemy
function mob.HandleSpawnedEnemy(newEnemy)
	-- Ensure the enemy has a Config folder
	local config = newEnemy:FindFirstChild("Config")
	if not config then
		config = Instance.new("Folder")
		config.Name = "Config"
		config.Parent = newEnemy
	end

	-- Add placeholders for modifiers if they don't exist
	if not config:FindFirstChild("Fortified") then
		local fortified = Instance.new("BoolValue")
		fortified.Name = "Fortified"
		fortified.Value = false
		fortified.Parent = config
	end

	if not config:FindFirstChild("Fleetfooted") then
		local fleetfooted = Instance.new("BoolValue")
		fleetfooted.Name = "Fleetfooted"
		fleetfooted.Value = false
		fleetfooted.Parent = config
	end

	-- Set up modifier change listeners
	for _, child in ipairs(config:GetChildren()) do
		if child:IsA("BoolValue") then
			local modifier = child.Name
			-- Apply the initial state of the modifier
			local modifierModule = ModifierManager.GetModifier(modifier)
			if modifierModule then
				ModifierManager.ApplyModifier(newEnemy, modifier, child.Value)
			end

			-- Listen for changes and adjust dynamically
			child.Changed:Connect(function(newValue)
				local modifierModule = ModifierManager.GetModifier(modifier)
				if modifierModule then
					ModifierManager.ApplyModifier(newEnemy, modifier, newValue)
				end
			end)
		end
	end
end

-- Function to optimize enemy for performance
function mob.Optimize(mobToOptimize)
	local humanoid = mobToOptimize:FindFirstChild("Humanoid")

	if mobToOptimize:FindFirstChild("HumanoidRootPart") then
		mobToOptimize.HumanoidRootPart:SetNetworkOwner(nil)
	elseif mobToOptimize.PrimaryPart ~= nil then
		mobToOptimize.PrimaryPart:SetNetworkOwner(nil)
	end

	if not humanoid then return end

	humanoid:SetStateEnabled(Enum.HumanoidStateType.Seated, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Running, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Climbing, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Landed, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
end

-- Function to enable death spawning on an enemy
function mob.EnableDeathSpawning(enemy, spawnData, map)
	if not enemy or not enemy:FindFirstChild("Humanoid") then
		warn("Invalid enemy provided to EnableDeathSpawning")
		return
	end

	-- Store the death spawn configuration, including the path index if available
	local pathIndex = enemy:GetAttribute("PathIndex")
	if not pathIndex then
		local pathIndexValue = enemy:FindFirstChild("PathIndex")
		if pathIndexValue and pathIndexValue:IsA("IntValue") then
			pathIndex = pathIndexValue.Value
		end
	end
	if pathIndex then
		spawnData._PathIndex = pathIndex -- store as a private field to avoid conflict with modifiers
	end
	if map then
		spawnData._Map = map -- store map reference if provided
	end
	deathSpawnConfigs[enemy] = spawnData
end

-- Function to handle enemy death and spawning
local function handleEnemyDeath(enemy)
	-- Check if this enemy has death spawn configuration
	local spawnData = deathSpawnConfigs[enemy]
	if spawnData then
		task.spawn(function()
			local map = spawnData._Map or workspace.Map:FindFirstChildOfClass("Folder")
			if map then
				local cframe = enemy.PrimaryPart.CFrame
				local movingToVal = enemy.MovingTo.Value
				local pathIndex = spawnData._PathIndex or 1

				-- Spawn the new enemies
				for i = 1, spawnData.quantity or 1 do
					local newEnemies = mob.Summon(
						spawnData.name or "Spawn2",
						1,
						map,
						cframe,
						movingToVal,
						pathIndex
					)

					-- Apply modifiers if provided
					if newEnemies and newEnemies[1] and spawnData.modifiers then
						modifyConfiguration(newEnemies[1], spawnData.modifiers)
					end
				end
			end
		end)

		-- Clean up the death spawn configuration
		deathSpawnConfigs[enemy] = nil
	end
end

-- Table to track all moving enemies
local ActiveEnemies = {}

-- Shared update loop for all enemies
RunService.Heartbeat:Connect(function(dt)
	for i = #ActiveEnemies, 1, -1 do
		local data = ActiveEnemies[i]
		local enemy = data.enemy
		if not enemy or not enemy.Parent or enemy.Humanoid.Health <= 0 then
			table.remove(ActiveEnemies, i)
			-- Do NOT destroy enemy here; let Humanoid.Died handler handle destruction and animation
			continue
		end

		-- Skip movement and facing if WalkSpeed is 0
		if enemy.Humanoid.WalkSpeed == 0 then
			continue
		end

		-- ShieldHealth/ShieldMaxHealth sync (only if ShieldHealth > ShieldMaxHealth)
		local config = enemy:FindFirstChild("Config")
		if config then
			local shield = config:FindFirstChild("ShieldHealth")
			local maxShield = config:FindFirstChild("ShieldMaxHealth")
			if shield and maxShield and shield.Value > maxShield.Value then
				maxShield.Value = shield.Value
			end
		end

		local hrp = enemy:FindFirstChild("HumanoidRootPart") or enemy.PrimaryPart
		if not hrp then continue end

		local targetWaypoint = data.waypoints[data.current]
		if not targetWaypoint then
			table.remove(ActiveEnemies, i)
			enemy:Destroy()
			continue
		end

		local targetPosition = targetWaypoint.Position + data.offset

		-- Only offset Y if HRP is larger than normal (Y > 2)
		local hrpSizeY = hrp.Size and hrp.Size.Y or 2
		local groundY
		if hrpSizeY > 2 then
			groundY = targetPosition.Y + hrpSizeY / 2
		else
			groundY = targetPosition.Y
		end
		local adjustedTargetPosition = Vector3.new(targetPosition.X, groundY, targetPosition.Z)

		if (hrp.Position - adjustedTargetPosition).Magnitude > 0.5 then
			local direction = (adjustedTargetPosition - hrp.Position).Unit
			local step = enemy.Humanoid.WalkSpeed * dt
			local hrppos = hrp.Position + direction * step
			-- Keep the bottom of the HRP on the ground for big enemies, else use normal Y
			hrppos = Vector3.new(hrppos.X, groundY, hrppos.Z)
			local currentLook = hrp.CFrame.LookVector
			local lerpAlpha = 0.15
			local newLook = currentLook:Lerp(direction, lerpAlpha).Unit
			local newCFrame = CFrame.new(hrppos, hrppos + newLook)
			hrp.CFrame = newCFrame
		else
			data.current = data.current + 1
			-- Update MovingTo.Value to match current waypoint
			local movingTo = enemy:FindFirstChild("MovingTo")
			if movingTo and typeof(movingTo) == "Instance" and movingTo:IsA("IntValue") then
				movingTo.Value = data.current
			end
			if data.current > #data.waypoints then
				-- Always reference the base as workspace.Map:FindFirstChildOfClass("Folder").Base.Humanoid
				local mapFolder = workspace:FindFirstChild("Map")
				local mapSubFolder = mapFolder and mapFolder:FindFirstChildOfClass("Folder")
				local baseModel = mapSubFolder and mapSubFolder:FindFirstChild("Base")
				local baseHumanoid = baseModel and baseModel:FindFirstChild("Humanoid")
				local enemyHumanoid = enemy:FindFirstChild("Humanoid")
				local damage = enemyHumanoid and enemyHumanoid.Health or 0
				if baseHumanoid and damage > 0 then
					baseHumanoid:TakeDamage(damage)
				end
				table.remove(ActiveEnemies, i)
				enemy:Destroy()
			end
		end
	end
end)

-- Helper to get all path folders and starts
local function getPathsAndStarts(map)
	local paths = {}
	local starts = {}

	local function recursiveSearch(parent)
		for _, child in ipairs(parent:GetChildren()) do
			-- Accept both "Path" and "Path1", "Path2", etc. (case-insensitive)
			if child:IsA("Folder") and child.Name:lower():match("^path%d*$") then
				table.insert(paths, child)
			end
			if child:IsA("BasePart") and child.Name:lower():match("^start%d*$") then
				table.insert(starts, child)
			end
			recursiveSearch(child)
		end
	end

	recursiveSearch(map)

	-- Sort paths: numbered first in order, then plain 'Path' last
	table.sort(paths, function(a, b)
		local na = tonumber(a.Name:match("%d+$"))
		local nb = tonumber(b.Name:match("%d+$"))
		if na and nb then
			return na < nb
		elseif na then
			return true -- a is numbered, b is not
		elseif nb then
			return false -- b is numbered, a is not
		else
			return a.Name < b.Name -- both unnumbered, fallback to name
		end
	end)
	-- Sort starts: numbered first in order, then plain 'Start' last
	table.sort(starts, function(a, b)
		local na = tonumber(a.Name:match("%d+$"))
		local nb = tonumber(b.Name:match("%d+$"))
		if na and nb then
			return na < nb
		elseif na then
			return true -- a is numbered, b is not
		elseif nb then
			return false -- b is numbered, a is not
		else
			return a.Name < b.Name -- both unnumbered, fallback to name
		end
	end)

	-- Debug print to show found starts
	local startNames = {}
	for _, s in ipairs(starts) do
		table.insert(startNames, s:GetFullName())
	end
	print("[Enemy] Starts found:", table.concat(startNames, ", "))

	return paths, starts
end
-- Local function to handle shielded damage automatically
local function handleShieldedDamage(enemy)
	local humanoid = enemy:FindFirstChild("Humanoid")
	local config = enemy:FindFirstChild("Config")
	if not humanoid or not config then return end
	local lastHealth = humanoid.Health
	humanoid.HealthChanged:Connect(function(newHealth)
		local shield = config:FindFirstChild("ShieldHealth")
		if shield and shield.Value > 0 and newHealth < lastHealth then
			local attemptedDamage = lastHealth - newHealth
			-- REMOVE health reset: humanoid.Health = lastHealth
			if attemptedDamage <= shield.Value then
				shield.Value = shield.Value - attemptedDamage
			else
				local leftover = attemptedDamage - shield.Value
				shield.Value = 0
				humanoid.Health = math.max(humanoid.Health - leftover, 0)
			end
			lastHealth = humanoid.Health
			return
		end
		lastHealth = humanoid.Health
	end)
end
-- Function to create and setup a new enemy
local function createEnemy(name, map, cframe, movingToVal, isSpawned, pathIndex, modifiers)
	local mobExists = ServerStorage.Enemies:FindFirstChild(name)
	if not mobExists then
		warn("Requested mob does not exist:", name)
		return nil
	end

	local newMob = mobExists:Clone()
	newMob.HumanoidRootPart.CFrame = cframe
	newMob.Parent = workspace.Enemy
	newMob.HumanoidRootPart:SetNetworkOwner(nil)
	mob.Optimize(newMob)

	-- Set up MovingTo value
	local movingTo = Instance.new("IntValue")
	movingTo.Name = "MovingTo"
	movingTo.Value = movingToVal or 1
	movingTo.Parent = newMob

	-- Set up PathIndex attribute and IntValue for robust persistence
	local assignedPathIndex = pathIndex or 1
	newMob:SetAttribute("PathIndex", assignedPathIndex)
	local pathIndexValue = Instance.new("IntValue")
	pathIndexValue.Name = "PathIndex"
	pathIndexValue.Value = assignedPathIndex
	pathIndexValue.Parent = newMob

	-- Set up NormalSpeed
	local NormalSpeed = newMob.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
	NormalSpeed.Name = "NormalSpeed"
	NormalSpeed.Value = newMob.Humanoid.WalkSpeed
	NormalSpeed.Parent = newMob.Config

	-- Set up ShieldMaxHealth and ShieldHealth (default 0)
	local ShieldMaxHealth = newMob.Config:FindFirstChild("ShieldMaxHealth")
	if not ShieldMaxHealth then
		ShieldMaxHealth = Instance.new("NumberValue")
		ShieldMaxHealth.Name = "ShieldMaxHealth"
		ShieldMaxHealth.Value = 0
		ShieldMaxHealth.Parent = newMob.Config
	end

	local ShieldHealth = newMob.Config:FindFirstChild("ShieldHealth")
	if not ShieldHealth then
		ShieldHealth = Instance.new("NumberValue")
		ShieldHealth.Name = "ShieldHealth"
		ShieldHealth.Value = 0
		ShieldHealth.Parent = newMob.Config
	end

	-- Always sync ShieldMaxHealth to ShieldHealth if ShieldHealth is higher
	if ShieldHealth.Value > ShieldMaxHealth.Value then
		ShieldMaxHealth.Value = ShieldHealth.Value
	end

	-- Listen for ShieldHealth changes and update ShieldMaxHealth if needed
	ShieldHealth.Changed:Connect(function(newValue)
		if newValue > ShieldMaxHealth.Value then
			ShieldMaxHealth.Value = newValue
		end
	end)

	-- Set NoMoney flag for summoned enemies
	if not isSpawned then
		local NoMoney = newMob.Config:FindFirstChild("NoMoney") or Instance.new("BoolValue")
		NoMoney.Value = true
		NoMoney.Name = "NoMoney"
		NoMoney.Parent = newMob.Config
	end

	-- Handle spawned enemy setup
	task.spawn(function()
		mob.HandleSpawnedEnemy(newMob)
	end)

	-- Apply modifiers (including Armed) if provided, passing intendedCFrame
	if modifiers then
		modifyConfiguration(newMob, modifiers, cframe)
	end

	-- Set collision group
	for _, object in ipairs(newMob:GetDescendants()) do
		if object:IsA("BasePart") then
			object.CollisionGroup = "Enemy"
		end
	end

	-- Set up death handler
	newMob.Humanoid.Died:Connect(function()
		-- Handle death spawning first
		handleEnemyDeath(newMob)

		-- Call modifier OnDeath logic (e.g., Petrified, others)
		ModifierManager.OnDeath(newMob)

		-- Play death sound
		local DeathSound = newMob:FindFirstChild("Head") and newMob.Head:FindFirstChild("DeathSound")
		if DeathSound then
			DeathSound:Play()
		end

		-- Anchor the enemy
		local hrp = newMob:FindFirstChild("HumanoidRootPart") or newMob.PrimaryPart
		if hrp then
			hrp.Anchored = true
		end

		-- Play death animation if available
		if newMob:FindFirstChild("Animations") and newMob.Animations:FindFirstChild("DeathAnim") then
			local deathAnim = newMob.Animations.DeathAnim
			ContentProvider:PreloadAsync({deathAnim})
			local animationTrack = newMob.Humanoid:LoadAnimation(deathAnim)
			animationTrack:Play()

			task.spawn(function()
				task.wait(animationTrack.Length - 0.1)
				newMob:Destroy()
			end)
		else
			task.wait(0.5)
			newMob:Destroy()
		end
	end)

	-- Set up shielded damage handling
	handleShieldedDamage(newMob)

	-- Start movement
	mob.Move(newMob, map, movingToVal)

	return newMob
end

-- Module-level variable to track last used start index for round-robin
local lastStartIndex = 0
-- Module-level variable to track last used path index for round-robin
local lastPathIndex = 0

-- Update mob.Spawn to use module-level round-robin indices
function mob.Spawn(name, quantity, map, modifiers)
	local SpawnedEnemies = {}
	local paths, starts = getPathsAndStarts(map)
	-- Filter numbered paths for round-robin cycling
	local numberedPaths = {}
	for _, p in ipairs(paths) do
		if p.Name:match("^Path%d+$") then
			table.insert(numberedPaths, p)
		end
	end
	local useNumbered = #numberedPaths > 0
	local cyclingPaths = useNumbered and numberedPaths or paths
	local useMulti = (#cyclingPaths > 1) or (#starts > 1)
	local numPaths, numStarts
	local pathFolders = nil
	if not useMulti then
		-- Fallback to old logic
		for _ = 1, quantity do
			task.wait(0.5)
			local newMob = createEnemy(name, map, map.Start.CFrame, 1, true, 1, modifiers)
			if newMob then
				table.insert(SpawnedEnemies, newMob)
			end
		end
		return SpawnedEnemies
	end

	local isMultiple = map:FindFirstChild("Multiple") ~= nil
	if isMultiple then
		numPaths = math.max(1, #cyclingPaths)
		numStarts = math.max(1, #starts)
		for pathIndex = 1, numPaths do
			local startIndex = ((pathIndex - 1) % numStarts) + 1
			local start = starts[startIndex] or starts[1]
			for _ = 1, quantity do
				task.wait(0.5)
				local newMob = createEnemy(name, map, start.CFrame, 1, true, pathIndex, modifiers)
				if newMob then
					table.insert(SpawnedEnemies, newMob)
				end
			end
		end
		return SpawnedEnemies
	else
		numPaths = math.max(1, #cyclingPaths)
		numStarts = math.max(1, #starts)
	end

	-- If boss, always use path1 and first start
	local isBoss = name:lower():find("boss")
	if isBoss then
		local start = starts[1] or cyclingPaths[1]:FindFirstChild("Start")
		for _ = 1, quantity do
			task.wait(0.5)
			local newMob = createEnemy(name, map, start.CFrame, 1, true, 1, modifiers)
			if newMob then
				table.insert(SpawnedEnemies, newMob)
			end
		end
		return SpawnedEnemies
	end

	-- Without Multiple: independent round-robin for starts and paths (module-level)
	for _ = 1, quantity do
		task.wait(0.5)
		lastStartIndex = ((lastStartIndex) % numStarts) + 1
		lastPathIndex = ((lastPathIndex) % numPaths) + 1
		local start = starts[lastStartIndex] or starts[1]
		local pathIndex = lastPathIndex
		if pathIndex < 1 or pathIndex > numPaths then
			pathIndex = 1
		end
		local newMob = createEnemy(name, map, start.CFrame, 1, true, pathIndex, modifiers)
		if newMob then
			table.insert(SpawnedEnemies, newMob)
		end
	end
	return SpawnedEnemies
end

-- Update mob.Move to use cyclingPaths for path lookup
function mob.Move(enemy, map, movingToVal)
	local _humanoid = enemy:WaitForChild("Humanoid") -- unused, silence lint
	local hrp = enemy:FindFirstChild("HumanoidRootPart") or enemy.PrimaryPart
	local paths, _ = getPathsAndStarts(map)
	local numberedPaths = {}
	for _, p in ipairs(paths) do
		if p.Name:match("^Path%d+$") then
			table.insert(numberedPaths, p)
		end
	end
	local useNumbered = #numberedPaths > 0
	local cyclingPaths = useNumbered and numberedPaths or paths
	local pathIndex = enemy:GetAttribute("PathIndex") or (enemy:FindFirstChild("PathIndex") and enemy.PathIndex.Value) or 1
	if pathIndex < 1 or pathIndex > #cyclingPaths then
		warn("[Enemy] Invalid PathIndex:", pathIndex, "for enemy:", enemy.Name, "(resetting to 1)")
		pathIndex = 1
		enemy:SetAttribute("PathIndex", 1)
		local pathIndexValue = enemy:FindFirstChild("PathIndex")
		if pathIndexValue and pathIndexValue:IsA("IntValue") then
			pathIndexValue.Value = 1
		end
	end
	local waypoints
	local pathFolder = cyclingPaths[pathIndex] or cyclingPaths[1]
	if pathFolder then
		print("[Enemy] Moving enemy:", enemy.Name, "PathIndex:", pathIndex, "Using path folder:", pathFolder:GetFullName())
		waypoints = {}
		for _, child in ipairs(pathFolder:GetChildren()) do
			if child:IsA("BasePart") then
				table.insert(waypoints, child)
			end
		end
	else
		-- Fallback: use Waypoints folder's direct children
		local waypointsFolder = map:FindFirstChild("Waypoints")
		if waypointsFolder then
			waypoints = {}
			for _, child in ipairs(waypointsFolder:GetChildren()) do
				if child:IsA("BasePart") then
					table.insert(waypoints, child)
				end
			end
		else
			waypoints = nil
		end
	end
	if not waypoints or #waypoints == 0 or not hrp then
		warn("Waypoints or HumanoidRootPart not found or empty in map:", map.Name or "Unknown")
		return
	end
	-- Sort waypoints: numeric names first in order, then non-numeric names alphabetically
	table.sort(waypoints, function(a, b)
		local na = tonumber(a.Name)
		local nb = tonumber(b.Name)
		if na and nb then
			return na < nb
		elseif na then
			return true -- a is numeric, b is not
		elseif nb then
			return false -- b is numeric, a is not
		else
			return tostring(a.Name) < tostring(b.Name) -- both non-numeric, sort alphabetically
		end
	end)
	local offset = Vector3.new(math.random(-50, 50) / 100, 2, math.random(-50, 50) / 100)
	local startWaypoint
	if typeof(movingToVal) == "Instance" and movingToVal:IsA("ValueBase") then
		local valueProp = rawget(movingToVal, "Value")
		if typeof(valueProp) == "number" then
			startWaypoint = valueProp
		else
			startWaypoint = 1
		end
	elseif typeof(movingToVal) == "number" then
		startWaypoint = movingToVal
	else
		startWaypoint = 1
	end
	local localHrp = hrp
	localHrp.Anchored = true
	-- Only add to ActiveEnemies if WalkSpeed is not 0
	if enemy.Humanoid.WalkSpeed ~= 0 then
		table.insert(ActiveEnemies, {
			enemy = enemy,
			waypoints = waypoints,
			current = startWaypoint,
			offset = offset,
			map = map
		})
	end
end

-- Function to summon enemies at specific location and waypoint
function mob.Summon(name, quantity, map, cframe, MovingToVal, PathIndexOverride, modifiers)
	local SpawnedEnemies = {}
	for _ = 1, quantity do
		task.wait(0.5)
		local newMob = createEnemy(name, map, cframe, MovingToVal, false, PathIndexOverride, modifiers)
		if newMob then
			table.insert(SpawnedEnemies, newMob)
		end
	end
	return SpawnedEnemies
end

-- Export the internalValues table for use in other modules
mob.InternalValues = internalValues

return mob
