local ReplicatedStorage = game:GetService("ReplicatedStorage")
local DebrisService = game:GetService("Debris")
local Players = game:GetService("Players")

local Particles = ReplicatedStorage:WaitForChild("Particles")
-- Remove the circular dependency
-- local EffectManager = require(script.Parent.Parent.EffectManager)

print("Dissolve effect module loaded!")

local DissolveEffect = {}

-- Helper function to send cash to players (copied from EffectManager)
local function SendCash(player, value)
    if not player then return end
    
    for _, plr in pairs(Players:GetPlayers()) do
        if plr ~= player and plr:FindFirstChild("Gold") then
            plr.Gold.Value += (value / #Players:GetPlayers() / 2)
        end
    end
    
    if player:FindFirstChild("Gold") then
        player.Gold.Value += value
        print("Sent", value, "gold to player", player.Name)
    else
        warn("Gold not found for player:", player.Name)
    end
end

function DissolveEffect.Apply(tower, target, duration, owner)
    print("DissolveEffect.Apply called with target:", target.Name, "duration:", duration)
    
    local humanoid = target:FindFirstChild("Humanoid")
    
    if not humanoid then 
        print("Target has no humanoid")
        return false
    end
    
    -- Check if target can be affected (boss, immunity, etc.)
    if target.Config:FindFirstChild("NoStun") and target.Config.NoStun.Value == true then
        print("Target has NoStun immunity")
        return false
    end
    
    if target.Config:FindFirstChild("IsBoss") and target.Config.IsBoss.Value == true then
        print("Target is a boss and immune to dissolve")
        return false
    end
    
    local MeltDamage = tower.Config:FindFirstChild("MeltDamage") and tower.Config.MeltDamage.Value or 1
    local MeltEffect = Particles:FindFirstChild("MeltEffect"):Clone()

    if not target.Torso:FindFirstChild("MeltEffect") and not target.HumanoidRootPart:FindFirstChild("MeltEffect") then
        MeltEffect.Parent = target.Torso or target.HumanoidRootPart
        MeltEffect.Color = ColorSequence.new(target.Torso.Color) or ColorSequence.new(1, 1, 1)
        
        -- Disable scripts during melt effect
        for _, v in pairs(target:GetChildren()) do
            if v:IsA("Script") then
                v.Enabled = false
            end
        end
    end
    
    task.spawn(function()
        for i = 1, duration do
            task.wait(1)
            if humanoid and humanoid.Parent then
                -- Check if we should give money for this damage
                local shouldGiveMoney = not target.Config:FindFirstChild("NoMoney") or target.Config.NoMoney.Value == false
                
                -- Calculate actual damage (respect health limits)
                local actualDamage = math.min(humanoid.Health, MeltDamage)
                
                -- Apply damage
                humanoid:TakeDamage(MeltDamage)
                
                -- Give money to player if applicable
                if shouldGiveMoney and owner and actualDamage > 0 then
                    SendCash(owner, actualDamage)
                    
                    -- Update damage dealt counter if it exists
                    if tower.Config:FindFirstChild("DamageDealt") then
                        tower.Config.DamageDealt.Value += actualDamage
                    end
                end
                
                print("Applied dissolve damage:", actualDamage)
            else
                break
            end
        end
        
        if target and target.Parent then
            DebrisService:AddItem(MeltEffect, 1)
            
            -- Re-enable scripts after effect ends
            for _, v in pairs(target:GetChildren()) do
                if v:IsA("Script") then
                    v.Enabled = true
                end
            end
        end
    end)
    
    print("Dissolve effect successfully applied to", target.Name)
    return true
end

return DissolveEffect


