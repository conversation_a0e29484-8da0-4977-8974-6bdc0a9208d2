-- Petrified.luau
-- Enemy modifier: upon death, leaves a petrified statue with special properties

local function applyPetrified(enemy)
	-- Clone the enemy and parent to the Enemy folder (not a separate Petrified folder)
	local enemyFolder = workspace:FindFirstChild("Enemy")
	if not enemyFolder then
		enemyFolder = Instance.new("Folder")
		enemyFolder.Name = "Enemy"
		enemyFolder.Parent = workspace
	end

	local petrified = enemy:Clone()
	petrified.Name = enemy.Name .. "_Petrified"
	petrified.Parent = enemyFolder

	-- Set health to 95% of original
	local humanoid = petrified:FindFirstChildOfClass("Humanoid")
	if humanoid then
		humanoid.Health = humanoid.MaxHealth * 0.95
		humanoid.MaxHealth = humanoid.MaxHealth * 0.95
		humanoid.WalkSpeed = 0
		-- Prevent WalkSpeed changes
		humanoid:GetPropertyChangedSignal("WalkSpeed"):Connect(function()
			humanoid.WalkSpeed = 0
		end)
	end

	-- Set Armor to 60%
	if petrified:FindFirstChild("Config") then
		local config = petrified.Config
		local armor = config:FindFirstChild("DefensePercent")
		local nomoney = config:FindFirstChild("NoMoney")  
		if not nomoney then
			nomoney = Instance.new("BoolValue")
			nomoney.Name = "NoMoney"
			nomoney.Parent = config
		end
		if not armor then
			armor = Instance.new("NumberValue")
			armor.Name = "DefensePercent"
			armor.Parent = config
		end
		armor.Value = 60
		nomoney.Value = true
	end

	-- Change all part textures and colors
	for _, part in ipairs(petrified:GetDescendants()) do
		if part:IsA("BasePart") then
			part.Material = Enum.Material.Basalt
			part.Color = Color3.new(0, 0, 0)
		end
	end

	-- Remove any scripts or behaviors that would make it move/attack
	for _, obj in ipairs(petrified:GetDescendants()) do
		if obj:IsA("Script") or obj:IsA("LocalScript") then
			obj:Destroy()
		end
	end

	-- Remove any equipped gun and related joints
	local gun = petrified:FindFirstChild("Gun")
	if gun then
		gun:Destroy()
	end
	for _, desc in ipairs(petrified:GetDescendants()) do
		if desc:IsA("Motor6D") and desc.Name == "GunMotor" then
			desc:Destroy()
		end
	end
	-- Clear Armed value in Config if present
	if petrified:FindFirstChild("Config") then
		local config = petrified.Config
		local armedValue = config:FindFirstChild("Armed")
		if armedValue then
			armedValue.Value = ""
		end
	end

	return petrified
end

-- Modifier API: call this in your enemy death handler if enemy has this modifier
return {
	Apply = function(enemy, enabled)
		-- Petrified does not need to do anything on apply, but this silences the warning
		return true
	end,
	OnDeath = function(enemy)
		return applyPetrified(enemy)
	end
}
