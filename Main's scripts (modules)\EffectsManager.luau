local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local Players = game:GetService("Players")

print("EffectManager loading...")

local EffectManager = {}
local effectModules = {}

-- Helper function to send cash to players
function EffectManager.SendCash(player, value, allowed)
	if not player then return end

	if allowed == nil then
		allowed = true
	end

	if allowed == true then
		for _, plr in pairs(Players:GetPlayers()) do
			if plr ~= player and plr:FindFirstChild("Gold") then
				plr.Gold.Value += (value / #Players:GetPlayers() / 2)
			end
		end
	end

	if player:FindFirstChild("Gold") then
		player.Gold.Value += value
	else
		warn("Gold not found for player:", player.Name)
	end
end

-- Function to face target (common utility used by many effects)
function EffectManager.FaceTarget(tower, target, duration)
	pcall(function()
		local targetVector = Vector3.new(target.PrimaryPart.Position.X, tower.PrimaryPart.Position.Y, target.PrimaryPart.Position.Z)
		local targetCFrame = CFrame.new(tower.PrimaryPart.Position, targetVector)
		local tweenInfo = TweenInfo.new(duration, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0)
		local faceTargetTween = TweenService:Create(tower.PrimaryPart, tweenInfo, {CFrame = targetCFrame})
		faceTargetTween:Play()
	end)
end

-- Function to load an effect module
function EffectManager.GetEffect(effectName)
	print("Attempting to load effect module:", effectName)

	if not effectModules[effectName] then
		local success, result = pcall(function()
			return require(script.Parent.Effects[effectName])
		end)

		if success then
			print("Successfully loaded effect module:", effectName)
			effectModules[effectName] = result
		else
			warn("Failed to load effect module:", effectName, "Error:", result)
			return nil
		end
	end

	return effectModules[effectName]
end

-- Function to apply an effect
function EffectManager.ApplyEffect(tower, target, effectName, duration)


	if not target or not target:FindFirstChild("Humanoid") or target.Humanoid.Health <= 0 then
		print("Target invalid or dead")
		return false
	end

	-- Get the effect module
	local effectModule = EffectManager.GetEffect(effectName)
	if not effectModule then
		print("Effect module not found:", effectName)
		return false
	end

	-- Get tower owner for cash rewards
	local ownerName = tower.Config:FindFirstChild("Owner") and tower.Config.Owner.Value
	local owner = ownerName and Players:FindFirstChild(ownerName)

	-- Pass owner to effect module for cash rewards

	local success, result = pcall(function()
		return effectModule.Apply(tower, target, duration, owner)
	end)

	if not success then
		warn("Error applying effect:", result)
		return false
	end

	return result
end

-- Function to apply all effects from a tower to a target (soft-coded, modular)
function EffectManager.ApplyEffects(tower, target)
	if not tower or not target or not tower:FindFirstChild("Config") then return end
	local config = tower.Config

	-- Build a set of valid effect module names from the Effects folder
	local effectsFolder = script.Parent:FindFirstChild("Effects")
	if not effectsFolder then
		warn("[EffectManager] Effects folder not found!")
		return
	end
	local validEffects = {}
	for _, mod in ipairs(effectsFolder:GetChildren()) do
		if mod:IsA("ModuleScript") then
			validEffects[mod.Name] = true
		end
	end

	-- Only process config values that match a valid effect module
	for _, v in ipairs(config:GetChildren()) do
		if (v:IsA("NumberValue") or v:IsA("StringValue")) and validEffects[v.Name] then
			local effectModule = EffectManager.GetEffect(v.Name)
			if effectModule and type(effectModule.Apply) == "function" then
				local ownerName = config:FindFirstChild("Owner") and config.Owner.Value
				local owner = ownerName and Players:FindFirstChild(ownerName)
				local ok, result = pcall(function()
					return effectModule.Apply(tower, target, v.Value, owner)
				end)
				if not ok then
					warn("[EffectManager] Error applying effect:", v.Name, result)
				end
			end
		end
	end

	-- Handle EffectType/EffectDuration generic effect
	local effectTypeObj = config:FindFirstChild("EffectType")
	if effectTypeObj and effectTypeObj:IsA("StringValue") then
		local effectName = effectTypeObj.Value
		if validEffects[effectName] then
			local effectModule = EffectManager.GetEffect(effectName)
			if effectModule and type(effectModule.Apply) == "function" then
				local duration = 1
				local effectDurationObj = config:FindFirstChild("EffectDuration")
				if effectDurationObj and effectDurationObj.Value then
					duration = effectDurationObj.Value
				end
				local ownerName = config:FindFirstChild("Owner") and config.Owner.Value
				local owner = ownerName and Players:FindFirstChild(ownerName)
				local ok, result = pcall(function()
					return effectModule.Apply(tower, target, duration, owner)
				end)
				if not ok then
					warn("[EffectManager] Error applying effect via EffectType:", effectName, result)
				end
			end
		end
	end
end

-- Preload all effect modules
function EffectManager.Init()
	print("Initializing EffectManager...")

	-- Try to load the Effects folder
	local effectsFolder = script.Parent:FindFirstChild("Effects")
	if not effectsFolder then
		warn("Effects folder not found!")
		return
	end


end

-- Initialize the manager
EffectManager.Init()

return EffectManager




