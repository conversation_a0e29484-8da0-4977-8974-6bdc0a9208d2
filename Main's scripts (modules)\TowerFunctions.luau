-- TowerFunctions.luau
-- Utility functions for tower-related logic (e.g., cash rewards)

local Players = game:GetService("Players")

local TowerFunctions = {}

-- Determines if cash should be given for damaging this enemy
function TowerFunctions.ShouldGiveCash(target, damage)
	if not target or not target:Find<PERSON>irst<PERSON>hild("Config") then return false end
	local config = target.Config
	local shield = config:FindFirstChild("ShieldHealth")
	local nomoney = config:FindFirstChild("NoMoney")
	local humanoid = target:<PERSON><PERSON><PERSON><PERSON><PERSON>hild("Humanoid")
	if not humanoid then return false end

	-- If shield exists and is above 0, apply to shield first
	if shield and shield.Value > 0 then
		if damage <= shield.Value then
			return false -- All damage absorbed by shield
		end
		-- Some damage may go through, but only the part that isn't shielded
		damage = damage - shield.Value
	end

	local shieldDown = (not shield or shield.Value <= 0)
	local shouldGiveMoney = damage > 0 and shieldDown and (not nomoney or nomoney.Value == false)
	return shouldGiveMoney
end

-- Determines the cash reward for killing an enemy
function TowerFunctions.GetKillCash(target)
	if not target or not target:FindFirstChild("Config") then return 0 end
	local config = target.Config
	local humanoid = target:FindFirstChild("Humanoid")
	if not humanoid then return 0 end
	local moneyValue = config:FindFirstChild("Money")
	if moneyValue and typeof(moneyValue.Value) == "number" then
		return moneyValue.Value
	end
	return humanoid.MaxHealth or humanoid.Health or 0
end

-- Gives cash to all players: if solo, just X; if multiplayer, (X / player count) +10%
function TowerFunctions.SendCash(player, value, allowed)
	if allowed == nil then allowed = true end
	if allowed == true then
		local players = Players:GetPlayers()
		local playerCount = #players
		if playerCount == 1 then
			players[1].Gold.Value += value
		else
			local base = value / playerCount
			local payout = math.floor(base * 1.1 + 0.5) -- round to nearest whole number
			for _, plr in pairs(players) do
				plr.Gold.Value += payout
			end
		end
	else
		player.Gold.Value += value
	end
end

-- Handles milestone-based cash rewards for enemies with RepeatMoney
function TowerFunctions.HandleRepeatMoney(target, player)
	if not target or not target:FindFirstChild("Config") then return end
	local config = target.Config
	local humanoid = target:FindFirstChild("Humanoid")
	if not humanoid then return end

	local moneyValue = config:FindFirstChild("Money")
	local repeatMoney = config:FindFirstChild("RepeatMoney")
	local noMoney = config:FindFirstChild("NoMoney")
	if (noMoney and noMoney.Value == true) or not (moneyValue and repeatMoney and typeof(moneyValue.Value) == "number" and typeof(repeatMoney.Value) == "number") then
		return
	end

	-- Track the last milestone paid (0 to RepeatMoney)
	local lastMilestone = config:FindFirstChild("LastRepeatMoneyMilestone")
	if not lastMilestone then
		lastMilestone = Instance.new("IntValue")
		lastMilestone.Name = "LastRepeatMoneyMilestone"
		lastMilestone.Value = repeatMoney.Value
		lastMilestone.Parent = config
	end

	local maxHp = humanoid.MaxHealth
	local percentPerMilestone = 1 / repeatMoney.Value
	local currentPercent = humanoid.Health / maxHp
	local milestonesCrossed = math.floor((1 - currentPercent) / percentPerMilestone)

	while lastMilestone.Value > (repeatMoney.Value - milestonesCrossed) do
		TowerFunctions.SendCash(player, moneyValue.Value)
		lastMilestone.Value = lastMilestone.Value - 1
	end

	if humanoid.Health <= 0 and lastMilestone.Value > 0 then
		TowerFunctions.SendCash(player, moneyValue.Value)
		lastMilestone.Value = 0
	end
end

return TowerFunctions
