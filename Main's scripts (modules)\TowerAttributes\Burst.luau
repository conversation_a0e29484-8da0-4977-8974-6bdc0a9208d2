return function(attackFunc, tower, config)
	-- Ensure config values exist
	if not config:FindFirstChild("BurstAmount") then
		local burstAmount = Instance.new("NumberValue")
		burstAmount.Name = "BurstAmount"
		burstAmount.Value = 3
		burstAmount.Parent = config
	end
	if not config:FindFirstChild("BurstShots") then
		local burstShots = Instance.new("NumberValue")
		burstShots.Name = "BurstShots"
		burstShots.Value = 0
		burstShots.Parent = config
	end
	if not config:FindFirstChild("BurstCooldown") then
		local burstCooldown = Instance.new("NumberValue")
		burstCooldown.Name = "BurstCooldown"
		burstCooldown.Value = 0.75
		burstCooldown.Parent = config
	end
	if not config:FindFirstChild("IsBurstCooldown") then
		local isBurstCooldown = Instance.new("BoolValue")
		isBurstCooldown.Name = "IsBurstCooldown"
		isBurstCooldown.Value = false
		isBurstCooldown.Parent = config
	end

	return function(target, ...)
		local burstAmount = config:FindFirstChild("BurstAmount")
		local burstShots = config:FindFirstChild("BurstShots")
		local burstCooldown = config:FindFirstChild("BurstCooldown")
		local isBurstCooldown = config:FindFirstChild("IsBurstCooldown")

		if isBurstCooldown.Value then
			return -- Can't attack while in burst cooldown
		end

		if burstShots.Value >= burstAmount.Value then
			isBurstCooldown.Value = true
			task.spawn(function()
				task.wait(burstCooldown.Value)
				burstShots.Value = 0
				isBurstCooldown.Value = false
			end)
			return -- Can't attack while in burst cooldown
		end

		if target and target ~= "NO_TARGET" then
			attackFunc(target, ...)
			burstShots.Value = burstShots.Value + 1
		end
	end
end
