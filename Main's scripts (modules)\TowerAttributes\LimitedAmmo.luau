return function(attackFunc, tower, config)
	-- Ensure config values exist
	if not config:FindFirstChild("Ammo") then
		local ammo = Instance.new("NumberValue")
		ammo.Name = "Ammo"
		ammo.Value = 10
		ammo.Parent = config
	end
	if not config:FindFirstChild("MaxAmmo") then
		local maxAmmo = Instance.new("NumberValue")
		maxAmmo.Name = "MaxAmmo"
		maxAmmo.Value = 10
		maxAmmo.Parent = config
	end
	if not config:FindFirstChild("ReloadTime") then
		local reloadTime = Instance.new("NumberValue")
		reloadTime.Name = "ReloadTime"
		reloadTime.Value = 2
		reloadTime.Parent = config
	end
	if not config:FindFirstChild("IsReloading") then
		local isReloading = Instance.new("BoolValue")
		isReloading.Name = "IsReloading"
		isReloading.Value = false
		isReloading.Parent = config
	end

	return function(target, ...)
		local ammo = config:FindFirstChild("Ammo")
		local maxAmmo = config:FindFirstChild("MaxAmmo")
		local reloadTime = config:FindFirstChild("ReloadTime")
		local isReloading = config:FindFirstChild("IsReloading")

		-- Always forward NO_TARGET/nil to inner attributes so they can clear state
		if not target or target == "NO_TARGET" then
			attackFunc(target, ...)
			return
		end

		if isReloading.Value then
			return -- Can't attack while reloading
		end

		if ammo.Value <= 0 then
			isReloading.Value = true
			task.spawn(function()
				task.wait(reloadTime.Value)
				ammo.Value = maxAmmo.Value
				isReloading.Value = false
			end)
			return -- Can't attack while reloading
		end

		-- Only decrement ammo if actually attacking a target
		attackFunc(target, ...)
		ammo.Value = math.max(0, ammo.Value - 1)
	end
end
