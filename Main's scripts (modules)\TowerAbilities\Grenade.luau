-- Grenade.luau
-- Ability: Throws a grenade at the nearest enemy and triggers an explosion using the Explosion attribute wrapper

local Debris = game:GetService("Debris")
local TowerAttributes = script.Parent.Parent.TowerAttributes
local ExplosionWrapper = require(TowerAttributes:Wait<PERSON><PERSON><PERSON>hil<PERSON>("Explosion"))

local Grenade = {}

function Grenade.Activate(tower, player)
	-- Find nearest enemy
	local enemies = workspace:FindFirstChild("Enemy")
	if not enemies then return end
	local closest, minDist = nil, math.huge
	for _, enemy in ipairs(enemies:GetChildren()) do
		if enemy:<PERSON><PERSON><PERSON>t<PERSON>hil<PERSON>("HumanoidRootPart") and enemy:<PERSON><PERSON>irst<PERSON>hild("Humanoid") and enemy.Humanoid.Health > 0 then
			local dist = (enemy.HumanoidRootPart.Position - tower.PrimaryPart.Position).Magnitude
			if dist < minDist then
				closest, minDist = enemy, dist
			end
		end
	end
	if not closest then return end

	-- Create grenade part
	local grenade = Instance.new("Part")
	grenade.Shape = Enum.PartType.Ball
	grenade.Size = Vector3.new(1,1,1)
	grenade.BrickColor = BrickColor.new("Bright red")
	grenade.Material = Enum.Material.Metal
	grenade.Position = tower.PrimaryPart.Position + Vector3.new(0, 3, 0)
	grenade.Anchored = false
	grenade.CanCollide = false
	grenade.Parent = workspace
	Debris:AddItem(grenade, 5)

	-- Calculate velocity to arc grenade toward enemy
	local startPos = grenade.Position
	local endPos = closest.HumanoidRootPart.Position
	local gravity = workspace.Gravity
	local height = math.max(10, (endPos - startPos).Magnitude / 3)
	local displacement = endPos - startPos
	local flatDist = Vector3.new(displacement.X, 0, displacement.Z).Magnitude
	local time = math.sqrt((2 * height) / gravity) + math.sqrt((2 * (displacement.Y - height)) / gravity)
	if time < 0.5 then time = 0.5 end -- prevent too short
	local velocity = Vector3.new(displacement.X, 0, displacement.Z).Unit * (flatDist / time) + Vector3.new(0, height / time, 0)

	local bodyVelocity = Instance.new("BodyVelocity")
	bodyVelocity.Velocity = velocity
	bodyVelocity.MaxForce = Vector3.new(1e5, 1e5, 1e5)
	bodyVelocity.Parent = grenade

	-- On touch, trigger explosion wrapper
	local exploded = false
	grenade.Touched:Connect(function(hit)
		if exploded then return end
		if hit:IsDescendantOf(enemies) then
			exploded = true
			-- Use the Explosion attribute wrapper to do the explosion logic
			local config = tower:FindFirstChild("Config")
			local explosionFunc = ExplosionWrapper(function() end, tower, config)
			explosionFunc(closest)
			grenade:Destroy()
		end
	end)
end

return Grenade
