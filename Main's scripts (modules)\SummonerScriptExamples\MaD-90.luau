-- MaD-90 Portal Spawn Logic
-- Place this script inside MaD-90 enemy model

local mob = require(game.ServerScriptService.Main.Enemy)
local mad90 = script.Parent
local config = mad90:FindFirstChild("Config")
local map = workspace:FindFirstChild("Map") and workspace.Map:FindFirstChildOfClass("Folder")
local sound = mad90:Find<PERSON><PERSON>tChild("Head"):Find<PERSON>irstChild("Teleport")

while mad90.Parent do
	if not map then return end
	local hrp = mad90:FindFirstChild("HumanoidRootPart") or mad90.PrimaryPart
	if not hrp then return end
	sound:Play()
	local numToSpawn = math.random(3, 6)
	for i = 1, numToSpawn do
		local movingToVal = mad90:FindFirstChild("MovingTo") and mad90.MovingTo.Value or 1
		local pathIndex = mad90:GetAttribute("PathIndex") or (mad90:FindFirstChild("PathIndex") and mad90.PathIndex.Value) or 1
		-- You can change "SpawnedEnemy" to any enemy type you want to spawn
		mob.Summon("Spawn3", 1, map, hrp.CFrame, movingToVal, pathIndex)
		task.wait(0.2)
	end
	-- Portal effect: you can add a visual effect here if desired
	wait(6)
end
