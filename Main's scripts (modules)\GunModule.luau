-- GunModule.luau
-- Universal gun system for NPCs (R6)
-- Usage: local GunModule = require(...); GunModule.Equip(npc, gunModel)

local Players = game:GetService("Players")
local Debris = game:GetService("Debris")
local TweenService = game:GetService("TweenService")

local GunModule = {}

-- Table to track NPCs being auto-shot (use cancellation tokens)
local autoShootTokens = {}

-- Place these at the top of the module, after 'local GunModule = {}' and any required services
local needsRestore = {}
local cachedSpeed = {}

-- Helper: Attach gun to NPC's arm (R6)
local function attachGun(npc, gunModel, gripName, intendedCFrame)
	local arm = npc:FindFirstChild(gripName or "Right Arm")
	if not arm then return end
	local handle = gunModel:FindFirstChild("Handle") or gunModel.PrimaryPart or gunModel:FindFirstChildWhichIsA("BasePart")
	if not handle then return end
	-- Ensure PrimaryPart is set BEFORE parenting
	if not gunModel.PrimaryPart then
		gunModel.PrimaryPart = handle
		print("[GunModule] Set gun PrimaryPart to", handle.Name, "for", gunModel.Name)
	end

	-- Warn if handle is not at the origin (relative to gun model)
	if handle and (handle.Position - gunModel.PrimaryPart.Position).magnitude > 0.01 then
		warn("[GunModule] WARNING: Gun '" .. gunModel.Name .. "' Handle is not at the model origin! This will cause NPC offset issues. Please fix in Studio.")
	end

	-- Always use orientation-based offset logic for classic gun placement
	local orientation = nil
	local position = nil
	local gripString = gunModel:FindFirstChild("Grip")
	if gripString and gripString:IsA("StringValue") then
		local orientationValue = gripString:FindFirstChild("Orientation")
		if orientationValue and orientationValue:IsA("Vector3Value") then
			orientation = orientationValue.Value
		end
		local positionValue = gripString:FindFirstChild("Position")
		if positionValue and positionValue:IsA("Vector3Value") then
			position = positionValue.Value
		end
	end
	-- Calculate offset: place handle at arm's end, apply orientation and position if present
	local offset = CFrame.new(0, -arm.Size.Y/2, 0)
	-- Remove scaling for default-sized NPCs (default HRP size ~1.216, 1.216, 0.608)
	if position then
		local hrp = npc:FindFirstChild("HumanoidRootPart")
		local defaultHRPSize = Vector3.new(1.216, 1.216, 0.608)
		local hrpSize = hrp and hrp.Size or defaultHRPSize
		local isDefaultSize = math.abs(hrpSize.X - defaultHRPSize.X) < 0.05 and math.abs(hrpSize.Y - defaultHRPSize.Y) < 0.05 and math.abs(hrpSize.Z - defaultHRPSize.Z) < 0.05
		if isDefaultSize then
			offset = offset * CFrame.new(position)
		else
			local scale = arm.Size.Magnitude / 2 -- scale for non-default NPCs
			offset = offset * CFrame.new(position * scale)
		end
	end
	if orientation then
		offset = offset * CFrame.Angles(math.rad(orientation.X), math.rad(orientation.Y), math.rad(orientation.Z))
	end

	-- Parent the gun to the NPC before creating Motor6D
	gunModel.Parent = npc

	local motor = Instance.new("Motor6D")
	motor.Name = "GunMotor"
	motor.Part0 = arm
	motor.Part1 = handle
	motor.C0 = offset
	motor.C1 = CFrame.new()
	motor.Parent = arm

	-- Safety: restore NPC to intended position if provided
	if intendedCFrame and npc.PrimaryPart then
		npc:SetPrimaryPartCFrame(intendedCFrame)
	end

	return motor
end

-- Helper: Get stat value from gun's Stats folder
local function getStat(gun, statName, default)
	local stats = gun:FindFirstChild("Stats")
	if stats and stats:FindFirstChild(statName) then
		return stats[statName].Value
	end
	return default
end

-- Helper: Play gun sound
local function playSound(gun, soundName)
	local sounds = gun:FindFirstChild("Sounds")
	if sounds and sounds:FindFirstChild(soundName) then
		sounds[soundName]:Play()
	end
end

-- Helper: Play gun animation (if present)
local function playGunAnimation(npc, gun, animType)
	local anims = gun:FindFirstChild("Animations")
	if not anims then return end
	local humanoid = npc:FindFirstChild("Humanoid")
	if not humanoid then return end
	local animator = humanoid:FindFirstChild("Animator")
	if not animator then
		animator = Instance.new("Animator")
		animator.Parent = humanoid
	end
	local animObj
	if animType == "WalkAnim" then
		animObj = anims:FindFirstChild("WalkAnim")
	else
		animObj = anims:FindFirstChild(animType)
	end
	if animObj then
		local track = animator:LoadAnimation(animObj)
		track:Play()
		return track
	end
	return nil
end

-- Helper: Override NPC walk animation if gun has WalkAnim
local function overrideWalkAnimation(npc, gun)
	local anims = gun:FindFirstChild("Animations")
	if not anims then return end
	local walkAnim = anims:FindFirstChild("WalkAnim")
	if walkAnim and walkAnim:IsA("Animation") then
		local animFolder = npc:FindFirstChild("Animations")
		if animFolder then
			local walkAnimObj = animFolder:FindFirstChild("WalkAnim")
			if walkAnimObj and walkAnimObj:IsA("Animation") then
				-- Cache original AnimationId if not already cached
				if not walkAnimObj:GetAttribute("OriginalId") then
					walkAnimObj:SetAttribute("OriginalId", walkAnimObj.AnimationId)
				end
				local oldId = walkAnimObj.AnimationId
				walkAnimObj.AnimationId = walkAnim.AnimationId
				print("[GunModule] Set WalkAnim AnimationId for", npc.Name, "to", walkAnim.AnimationId)
				-- Force reload walk animation if AnimationId changed
				if oldId ~= walkAnim.AnimationId then
					local humanoid = npc:FindFirstChild("Humanoid")
					if humanoid then
						local animator = humanoid:FindFirstChild("Animator")
						if not animator then
							animator = Instance.new("Animator")
							animator.Parent = humanoid
						end
						-- Stop all current walk tracks
						for _, track in ipairs(animator:GetPlayingAnimationTracks()) do
							if track.Animation.AnimationId == oldId then
								track:Stop(0.1)
							end
						end
						-- Play new walk animation
						local newTrack = animator:LoadAnimation(walkAnimObj)
						newTrack:Play()
					end
				end
			else
				warn("[GunModule] No WalkAnim Animation object found in NPC's Animations folder for", npc.Name)
			end
		else
			warn("[GunModule] No Animations folder found in NPC for", npc.Name)
		end
	end
end

-- Helper: Find closest player or unit to NPC within range
local function getClosestTarget(npc, range)
	local closest, minDist = nil, range or 100
	-- First, check for alive players
	for _, player in ipairs(Players:GetPlayers()) do
		local char = player.Character
		if char and char:FindFirstChild("HumanoidRootPart") and char:FindFirstChild("Humanoid") and char.Humanoid.Health > 0 then
			local dist = (char.HumanoidRootPart.Position - npc.PrimaryPart.Position).Magnitude
			if dist < minDist then
				closest = char
				minDist = dist
			end
		end
	end
	-- If no player found, check for Units in workspace.Unit
	if not closest then
		local unitFolder = workspace:FindFirstChild("Unit")
		if unitFolder then
			for _, unit in ipairs(unitFolder:GetChildren()) do
				if unit:IsA("Model") and unit:FindFirstChild("HumanoidRootPart") and unit:FindFirstChild("Humanoid") and unit.Humanoid.Health > 0 then
					local dist = (unit.HumanoidRootPart.Position - npc.PrimaryPart.Position).Magnitude
					if dist < minDist then
						closest = unit
						minDist = dist
					end
				end
			end
		end
	end
	return closest
end

-- Helper: Predict target position after t seconds (linear prediction, less sensitive Y, with Spread stat)
local function predictPosition(target, t, gun)
	local hrp = target:FindFirstChild("HumanoidRootPart")
	if not hrp then return target.Position end
	local humanoid = target:FindFirstChild("Humanoid")
	if not humanoid then return hrp.Position end
	local velocity = hrp.Velocity or Vector3.new()
	local predicted = hrp.Position + velocity * t
	-- Blend Y axis: mostly use current Y, only a small part of predicted Y
	predicted = Vector3.new(predicted.X, hrp.Position.Y + (predicted.Y - hrp.Position.Y) * 0.2, predicted.Z)
	-- Spread: 0 = full prediction, 1 = always aim at current position
	local spread = getStat(gun, "Spread", 0.2) -- default 0.2 (20%)
	if spread > 1 then
		spread = spread / 100
	end -- allow 1-100% or 0-1
	predicted = hrp.Position * spread + predicted * (1 - spread)
	return predicted
end

-- Helper: Raycast for robust hit detection
local function raycastForPlayers(startPos, endPos, ignoreList)
	local direction = (endPos - startPos)
	local rayParams = RaycastParams.new()
	rayParams.FilterType = Enum.RaycastFilterType.Exclude
	rayParams.FilterDescendantsInstances = ignoreList or {}
	rayParams.IgnoreWater = true
	local result = workspace:Raycast(startPos, direction, rayParams)
	return result
end

-- Helper: Require all ModuleScripts in a folder and return as a table
local function requireGunScripts(scriptsFolder)
	local scripts = {}
	if scriptsFolder then
		for _, child in ipairs(scriptsFolder:GetChildren()) do
			if child:IsA("ModuleScript") then
				local ok, result = pcall(require, child)
				if ok then
					print("[GunModule] Loaded gun script:", child.Name)
					table.insert(scripts, result)
				else
					warn("[GunModule] Error requiring gun script:", child.Name, result)
				end
			end
		end
	else
		print("[GunModule] No Scripts folder found in gun model.")
	end
	return scripts
end

-- Helper: Fire a quicktime event bullet (non-physical, warning trail, then fast bullet)
local function fireBullet(npc, gun, target)
	local handle = gun:FindFirstChild("Handle") or gun.PrimaryPart or gun:FindFirstChildWhichIsA("BasePart")
	if not handle or not target or not target:FindFirstChild("HumanoidRootPart") then return end
	local humanoid = target:FindFirstChild("Humanoid")
	if not humanoid or humanoid.Health <= 0 then return end

	local bulletFireTime = getStat(gun, "BulletFireTime", 0.3)
	local damage = getStat(gun, "Damage", 2)
	local BulletSize = getStat(gun, "BulletSize", 0.1)
	local BulletColor = getStat(gun, "BulletColor", Color3.new(1, 1, 0))
	local fadeTime = 0.5
	local extendDistance = 200
	

	-- Predict where the player will be after bulletFireTime, using Spread
	local predictedPos = predictPosition(target, bulletFireTime, gun)
	local startPos = handle.Position
	local dir = (predictedPos - startPos).Unit
	local distance = (predictedPos - startPos).Magnitude
	local bulletEnd = predictedPos

	-- Ensure Bullets folder exists in workspace
	local bulletsFolder = workspace:FindFirstChild("Bullets")
	if not bulletsFolder then
		bulletsFolder = Instance.new("Folder")
		bulletsFolder.Name = "Bullets"
		bulletsFolder.Parent = workspace
	end

	-- Show warning trail (red) for bulletFireTime seconds
	local warningPart = Instance.new("Part")
	warningPart.Anchored = true
	warningPart.CanCollide = false
	warningPart.Size = Vector3.new(BulletSize, BulletSize, distance)
	local offset = CFrame.new(0, 0, -distance / 2)
	warningPart.CFrame = CFrame.new(startPos, predictedPos) * offset
	warningPart.Color = Color3.new(1, 0, 0)
	warningPart.Material = Enum.Material.Neon
	warningPart.Transparency = 0.3
	warningPart.Parent = bulletsFolder

	local warningTween = TweenService:Create(warningPart, TweenInfo.new(bulletFireTime, Enum.EasingStyle.Linear), {Transparency = 0.7})
	warningTween:Play()
	Debris:AddItem(warningPart, bulletFireTime + fadeTime)

	-- Spawn robust hitbox at warning part location (predicted endpoint)
	local hitbox = Instance.new("Part")
	local hitboxSize = getStat(gun, "HitboxSize", Vector3.new(1, 1, 1))
	hitbox.Size = hitboxSize
	hitbox.Anchored = true
	hitbox.CanCollide = false
	hitbox.Transparency = 0
	hitbox.Color = Color3.new(1, 0, 0)
	hitbox.CFrame = CFrame.new(predictedPos)
	hitbox.Parent = bulletsFolder
	Debris:AddItem(hitbox, bulletFireTime + fadeTime)

	task.wait(bulletFireTime)

	if warningPart then
		warningPart:Destroy()
	end

	-- At the moment of shooting, check for valid target in hitbox using GetPartBoundsInBox
	local foundTarget = nil
	local regionCFrame = hitbox.CFrame
	local regionSize = hitbox.Size
	local partsInBox = workspace:GetPartBoundsInBox(regionCFrame, regionSize)
	for _, part in ipairs(partsInBox) do
		local char = part.Parent
		if char and char:FindFirstChild("Humanoid") and char:FindFirstChild("HumanoidRootPart") then
			-- Ignore enemies in workspace.Enemy
			local isEnemy = workspace:FindFirstChild("Enemy") and char:IsDescendantOf(workspace.Enemy)
			if not isEnemy then
				if (char.HumanoidRootPart.Position - predictedPos).Magnitude < 2 then
					foundTarget = char
					break
				end
			end
		end
	end

	if foundTarget then
		bulletEnd = foundTarget.HumanoidRootPart.Position
		foundTarget.Humanoid:TakeDamage(damage)
		-- Call Gun Scripts if present
		local scriptsFolder = gun:FindFirstChild("Scripts")
		local gunScripts = requireGunScripts(scriptsFolder)
		print("[GunModule] Calling OnPlayerShot for scripts:", gunScripts)
		for _, scriptModule in ipairs(gunScripts) do
			if type(scriptModule.OnPlayerShot) == "function" then
				print("[GunModule] Calling OnPlayerShot in", scriptModule)
				-- Provide context: player, npc, gun, etc.
				scriptModule.OnPlayerShot({
					npc = npc,
					gun = gun,
					target = foundTarget,
					player = Players:GetPlayerFromCharacter(foundTarget),
				})
			end
		end
	else
		bulletEnd = predictedPos + dir * extendDistance
	end

	-- Fire the actual bullet (yellow trail, fast, non-physical)
	local bulletDistance = (bulletEnd - startPos).Magnitude
	local bulletPart = Instance.new("Part")
	bulletPart.Anchored = true
	bulletPart.CanCollide = false
	bulletPart.Size = Vector3.new(BulletSize, BulletSize, bulletDistance)
	local bulletOffset = CFrame.new(0, 0, -bulletDistance / 2)
	bulletPart.CFrame = CFrame.new(startPos, bulletEnd) * bulletOffset
	bulletPart.Color = BulletColor
	bulletPart.Material = Enum.Material.Neon
	bulletPart.Transparency = 0.1
	bulletPart.Parent = bulletsFolder

	-- Tween for fading effect
	local bulletTween = TweenService:Create(bulletPart, TweenInfo.new(fadeTime, Enum.EasingStyle.Linear), {Transparency = 1})
	bulletTween:Play()
	Debris:AddItem(bulletPart, fadeTime)

	-- Play sound and animation
	playSound(gun, "Shoot")
	playGunAnimation(npc, gun, "Shoot")
end

-- Main equip function
function GunModule.Equip(npc, gunModel, intendedCFrame)
	-- Save NPC position before equipping gun
	local originalCFrame = intendedCFrame or (npc.PrimaryPart and npc.PrimaryPart.CFrame)

	-- Remove old gun if present (force remove any child named 'Gun')
	local oldGun = npc:FindFirstChild("Gun")
	if oldGun then
		oldGun:Destroy()
	end
	-- Remove any leftover GunMotor
	for _, child in ipairs(npc:GetDescendants()) do
		if child:IsA("Motor6D") and child.Name == "GunMotor" then
			child:Destroy()
		end
	end

	-- Attach gun (this will parent it and create the joint)
	local grip = gunModel:FindFirstChild("Grip")
	local gripName = grip and grip.Value or "Right Arm"
	attachGun(npc, gunModel, gripName, originalCFrame)
	-- Apply walk speed deplete
	local wsDeplete = getStat(gunModel, "WalkSpeedDeplete", 1)
	if wsDeplete < 1 then
		local humanoid = npc:FindFirstChild("Humanoid")
		if humanoid then
			humanoid.WalkSpeed = humanoid.WalkSpeed * wsDeplete
		end
		-- Also apply to NormalSpeed in Config if present
		local config = npc:FindFirstChild("Config")
		if config then
			local normalSpeed = config:FindFirstChild("NormalSpeed")
			if normalSpeed and normalSpeed:IsA("NumberValue") then
				normalSpeed.Value = normalSpeed.Value * wsDeplete
			end
		end
	end
	-- Override walk animation if gun has WalkAnim
	overrideWalkAnimation(npc, gunModel)
	-- Store gun reference as a child named 'Gun' (rename only, do not reparent)
	gunModel.Name = "Gun"
	-- Do NOT reparent gunModel here; it is already parented in attachGun

	-- Restore NPC position after equipping gun
	if originalCFrame and npc.PrimaryPart then
		npc:SetPrimaryPartCFrame(originalCFrame)
	end

	-- Robustly stop any previous auto-shoot loop for this NPC
	if autoShootTokens[npc] then
		autoShootTokens[npc].cancelled = true
	end
	local token = {cancelled = false}
	autoShootTokens[npc] = token
	task.spawn(function()
		GunModule.AutoShoot(npc, token)
		autoShootTokens[npc] = nil
	end)
end

-- Main shoot function
function GunModule.ShootAt(npc, target)
	local gun = npc:FindFirstChild("Gun")
	if not gun then return end
	fireBullet(npc, gun, target)
end

-- Auto-shoot loop (call this in your NPC logic)
function GunModule.AutoShoot(npc, token)
	local waitedFirst = false
	while npc.Parent do
		if token and token.cancelled then
			break
		end
		local gun = npc:FindFirstChild("Gun")
		local humanoid = npc:FindFirstChild("Humanoid")
		local _config = npc:FindFirstChild("Config")
		if humanoid and (not needsRestore[npc]) and humanoid.WalkSpeed > 0 then
			cachedSpeed[npc] = humanoid.WalkSpeed
		end
		local normalSpeed = cachedSpeed[npc] or 16
		if humanoid then
			print("[GunModule]", npc.Name, "WalkSpeed:", humanoid.WalkSpeed, "needsRestore:", needsRestore[npc], "has gun:", gun ~= nil, "cachedSpeed:", cachedSpeed[npc])
		end
		if not gun then
			task.wait(0.5)
			continue
		end
		local fireRate = getStat(gun, "FireRate", 1)
		local range = getStat(gun, "Range", 100)
		local shootAmount = getStat(gun, "ShootAmount", 3)
		local reloadTime = getStat(gun, "ReloadTime", 2)
		local target = getClosestTarget(npc, range)
		if not waitedFirst then
			waitedFirst = true
			print("[GunModule]", npc.Name, "waiting before first shot:", reloadTime / 2)
			task.wait(reloadTime / 2)
		end
		local restoreSpeed = false
		if target and humanoid then
			-- Stop movement before shooting
			humanoid.WalkSpeed = 0
			needsRestore[npc] = true
			restoreSpeed = true
			for i = 1, shootAmount do
				-- Face the target horizontally before each shot
				local root = npc:FindFirstChild("HumanoidRootPart")
				local targetRoot = target:FindFirstChild("HumanoidRootPart")
				if root and targetRoot then
					local lookPos = Vector3.new(targetRoot.Position.X, root.Position.Y, targetRoot.Position.Z)
					local desiredCF = CFrame.new(root.Position, lookPos)
					root.CFrame = desiredCF
				end
				if token and token.cancelled then break end
				local gunNow = npc:FindFirstChild("Gun")
				if not (gunNow and target.Parent and target:FindFirstChild("HumanoidRootPart")) then
					print("[GunModule]", npc.Name, "shooting interrupted at shot", i)
					break
				end
				task.spawn(function()
					fireBullet(npc, gunNow, target)
				end)
				if i < shootAmount then
					task.wait(fireRate)
				else
					if humanoid and restoreSpeed then
						print("[GunModule]", npc.Name, "restoring WalkSpeed to", normalSpeed)
						humanoid.WalkSpeed = normalSpeed
						needsRestore[npc] = false
					end
					task.wait(reloadTime)
				end
				-- Ensure minimum fireRate delay even if fireBullet is very fast
				if i < shootAmount then
					task.wait(0.01)
				end
			end
		else
			-- Only restore speed if needsRestore is true (i.e., NPC was previously shooting)
			if humanoid and needsRestore[npc] then
				print("[GunModule]", npc.Name, "restoring WalkSpeed to", normalSpeed, "(no targets alive)")
				humanoid.WalkSpeed = normalSpeed
				needsRestore[npc] = false
			end
			task.wait(0.5)
		end
	end
end


-- Main apply function for use as a modifier
function GunModule.Apply(enemy, gunName, intendedCFrame)
	print("[GunModule] Apply called for enemy:", enemy and enemy.Name or 'nil', "with gun:", gunName)
	if not enemy then
		warn("[GunModule] Invalid enemy passed to Apply")
		return false
	end
	-- Ensure PrimaryPart is set
	if not enemy.PrimaryPart then
		local hrp = enemy:FindFirstChild("HumanoidRootPart")
		if hrp then
			enemy.PrimaryPart = hrp
			print("[GunModule] Set PrimaryPart to HumanoidRootPart for", enemy.Name)
		else
			warn("[GunModule] No HumanoidRootPart found to set as PrimaryPart for", enemy.Name)
		end
	end
	local config = enemy:FindFirstChild("Config")
	if config then
		local armedValue = config:FindFirstChild("Armed")
		if not armedValue then
			armedValue = Instance.new("StringValue")
			armedValue.Name = "Armed"
			armedValue.Parent = config
		end
		armedValue.Value = gunName or ""
	end
	if not gunName or gunName == "" then
		-- Remove gun and restore original walk animation if cached
		local oldGun = enemy:FindFirstChild("Gun")
		if oldGun then oldGun:Destroy() end
		for _, child in ipairs(enemy:GetDescendants()) do
			if child:IsA("Motor6D") and child.Name == "GunMotor" then
				child:Destroy()
			end
		end
		-- Restore original walk animation if cached
		local animFolder = enemy:FindFirstChild("Animations")
		if animFolder then
			local walkAnimObj = animFolder:FindFirstChild("WalkAnim")
			if walkAnimObj and walkAnimObj:IsA("Animation") and walkAnimObj:GetAttribute("OriginalId") then
				local origId = walkAnimObj:GetAttribute("OriginalId")
				if walkAnimObj.AnimationId ~= origId then
					walkAnimObj.AnimationId = origId
					local humanoid = enemy:FindFirstChild("Humanoid")
					if humanoid then
						local animator = humanoid:FindFirstChild("Animator")
						if not animator then
							animator = Instance.new("Animator")
							animator.Parent = humanoid
						end
						for _, track in ipairs(animator:GetPlayingAnimationTracks()) do
							track:Stop(0.1)
						end
						local newTrack = animator:LoadAnimation(walkAnimObj)
						newTrack:Play()
					end
				end
			end
		end
		return true
	end
	-- Ensure PrimaryPart is set
	if not enemy.PrimaryPart then
		local hrp = enemy:FindFirstChild("HumanoidRootPart")
		if hrp then
			enemy.PrimaryPart = hrp
			print("[GunModule] Set PrimaryPart to HumanoidRootPart for", enemy.Name)
		else
			warn("[GunModule] No HumanoidRootPart found to set as PrimaryPart for", enemy.Name)
		end
	end
	-- Always set Armed value in Config to match gunName (workaround for timing issues)
	local config = enemy:FindFirstChild("Config")
	if config then
		local armedValue = config:FindFirstChild("Armed")
		if not armedValue then
			armedValue = Instance.new("StringValue")
			armedValue.Name = "Armed"
			armedValue.Parent = config
		end
		armedValue.Value = gunName
	end
	local ServerStorage = game:GetService("ServerStorage")
	local gunsFolder = ServerStorage:FindFirstChild("Guns")
	if not gunsFolder then
		warn("[GunModule] Guns folder not found in ServerStorage")
		return false
	end
	local gunModel = gunsFolder:FindFirstChild(gunName)
	if not gunModel then
		warn("[GunModule] Gun model not found:", gunName)
		return false
	end
	local gunClone = gunModel:Clone()
	GunModule.Equip(enemy, gunClone, intendedCFrame)
	print("[GunModule] Equipped gun:", gunName, "to enemy:", enemy.Name)
	return true
end

return GunModule
