local Debris = game:GetService("Debris")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local events = ReplicatedStorage:WaitForChild("Events")
local animateTowerEvent = events:WaitForChild("AnimateTower")
local animateTowerChargeEvent = events:WaitForChild("AnimateTowerCharge")
local animateEnemyEvent = events:WaitForChild("AnimateEnemy")
local animateEnemyEventDeath = events:WaitForChild("AnimateEnemyDeath")
local animateUnitEvent = events:WaitForChild("AnimateUnit")

-- Cache table for NormalSpeed values
local normalSpeedCache = {}

-- Function to cache or retrieve NormalSpeed value
local function cacheNormalSpeedValue(target)
	if target and target:FindFirstChild("Config") then
		local cachedValue = normalSpeedCache[target] -- Retrieve from cache if available
		if cachedValue then
			return cachedValue
		end

		local normalSpeedValue
		if target.Config:FindFirstChild("NormalSpeed") then
			normalSpeedValue = target.Config.NormalSpeed.Value
		else
			normalSpeedValue = target.Humanoid.WalkSpeed
		end

		-- Store in cache for future use
		normalSpeedCache[target] = normalSpeedValue
		return normalSpeedValue
	end
	return nil
end

local function createPuddle(enemy,position)

	local puddle = Instance.new("Part")
	puddle.Shape = Enum.PartType.Cylinder
	puddle.Size = Vector3.new(enemy.Torso.Size, 0,0) -- Random size on Y and Z
	puddle.Position = enemy.PrimaryPart.Position - Vector3.new(math.random(-1.5,1.5), enemy.PrimaryPart.Size.Y * 1.5, math.random(-1.5,1.5))
	puddle.Anchored = true
	puddle.Orientation = Vector3.new(0, 90, 90)
	puddle.CanCollide = false
	puddle.Material = Enum.Material.Neon
	puddle.Color = enemy.Torso.Color
	puddle.Transparency = 0.4
	puddle.Parent = workspace

	-- Tween to flatten the puddle further
	local tweenInfo = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
	local tween = TweenService:Create(puddle, tweenInfo, {Size = Vector3.new(0.1, math.random(1.5,3), math.random(1.5,3))})

	-- Start tween and destroy the puddle after a delay
	tween:Play()
	tween.Completed:Connect(function()
		Debris:AddItem(puddle, 5) -- Remove puddle after 5 seconds
	end)
end

local function onEnemyDeath(enemy)
	local meltEffect = enemy.Torso:FindFirstChild("MeltEffect") or enemy.HumanoidRootPart:FindFirstChild("MeltEffect")
	local height = (enemy.PrimaryPart.Size.Y) - enemy.Humanoid.HipHeight
	local offset = Vector3.new(0, -height, 0)

	if meltEffect then
		-- Create 2 to 4 puddles
		local numPuddles = math.random(2, 4)
		for i = 1, numPuddles do
			-- Randomize position around the enemy's death position
			createPuddle(enemy,enemy.PrimaryPart.Position - offset)
		end
	end
end


local function WaitTime(Time)
	task.wait(Time)
end

local function fireProjectileBullet(tower, target, Time)
	-- Search for the "Aim" part in the tower
	local aimPart
	for _, descendant in pairs(tower:GetDescendants()) do
		if descendant:IsA("BasePart") and descendant.Name == "Aim" then
			aimPart = descendant
			break
		end
	end

	-- If the Aim part is not found, do not execute the function
	if not aimPart then
		return
	end

	local projectile = Instance.new("Part")
	local distance = (aimPart.Position - target.HumanoidRootPart.Position).Magnitude
	projectile.Size = Vector3.new(0.1, 0.1, distance)
	local offset = CFrame.new(0, 0, -distance / 2)
	projectile.CFrame = CFrame.new(aimPart.Position, target.HumanoidRootPart.Position) * offset
	projectile.Anchored = true 
	projectile.CanCollide = false
	local ProjectileFolder = tower.Config:FindFirstChild("ProjectileFolder")
	if ProjectileFolder and ProjectileFolder:FindFirstChild("ProjectileColor") then
		projectile.Color = ProjectileFolder.ProjectileColor.Value
	else
		projectile.Color = Color3.new(1, 0.933333, 0)
	end
	projectile.Material = Enum.Material.Neon
	projectile.Transparency = 0.3 
	projectile.Parent = workspace.Camera

	-- Tween for fading effect
	local tweenInfo = TweenInfo.new(Time, Enum.EasingStyle.Linear, Enum.EasingDirection.Out)
	local tween = TweenService:Create(projectile, tweenInfo, {Transparency = 1})

	-- Start fading the projectile
	tween:Play()
	tween.Completed:Connect(function()
		projectile:Destroy() -- Destroy the projectile after fading
	end)
end

local function fireUnitProjectileBullet(tower, target, Time)
	-- Search for the "Aim" part in the tower
	local aimPart
	for _, descendant in pairs(tower:GetDescendants()) do
		if descendant:IsA("BasePart") and descendant.Name == "Aim" then
			aimPart = descendant
			break
		end
	end

	-- If the Aim part is not found, do not execute the function
	if not aimPart then
		return
	end

	local projectile = Instance.new("Part")
	local distance = (aimPart.Position - target.HumanoidRootPart.Position).Magnitude
	projectile.Size = Vector3.new(0.1, 0.1, distance)
	local offset = CFrame.new(0, 0, -distance / 2)
	projectile.CFrame = CFrame.new(aimPart.Position, target.HumanoidRootPart.Position) * offset
	projectile.Anchored = true 
	projectile.CanCollide = false
	local ProjectileFolder = tower.Config:FindFirstChild("ProjectileFolder")
	if ProjectileFolder and ProjectileFolder:FindFirstChild("ProjectileColor") then
		projectile.Color = ProjectileFolder.ProjectileColor.Value
	else
		projectile.Color = Color3.new(1, 0.933333, 0)
	end
	projectile.Material = Enum.Material.Neon
	projectile.Transparency = 0.3 
	projectile.Parent = workspace.Camera

	-- Tween for fading effect
	local tweenInfo = TweenInfo.new(Time, Enum.EasingStyle.Linear, Enum.EasingDirection.Out)
	local tween = TweenService:Create(projectile, tweenInfo, {Transparency = 1})

	-- Start fading the projectile
	tween:Play()
	tween.Completed:Connect(function()
		projectile:Destroy() -- Destroy the projectile after fading
	end)
end


local function playAnimationForDuration(animationTrack, duration)
	local baseSpeed = (animationTrack.Length / duration) * 1.05
	animationTrack:Play()
	animationTrack:AdjustSpeed(baseSpeed)
end

local function setAnimation(object, animName)
	local humanoid = object:WaitForChild("Humanoid")
	local animationsFolder = object:FindFirstChild("Animations")

	if humanoid and animationsFolder then
		local animationObject = animationsFolder:WaitForChild(animName)

		if animationObject then
			local animator = humanoid:FindFirstChild("Animator") or Instance.new("Animator", humanoid)

			-- Stop any existing animations with the same name
			local playingTracks = animator:GetPlayingAnimationTracks()
			for _, track in pairs(playingTracks) do
				if track.Name == animName then
					return track -- Return the already playing track if found
				end
			end

			-- Check if the animation has an AnimSpeed modifier
			local animSpeedModifier = 1
			local animSpeedObject = animationObject:FindFirstChild("AnimSpeed")
			if animSpeedObject and animSpeedObject:IsA("NumberValue") then
				animSpeedModifier = animSpeedObject.Value
			end

			-- Load and return the new animation track
			local animationTrack = animator:LoadAnimation(animationObject)
			animationTrack.Name = animName

			-- Apply the AnimSpeed modifier to the animation
			animationTrack:AdjustSpeed(animSpeedModifier)

			return animationTrack
		end
	end
end

local function updateWalkAnimationSpeed(humanoid, animationTrack)
	-- Retrieve cached value or default to humanoid's walk speed
	local defaultWalkSpeed = normalSpeedCache[humanoid.Parent] or humanoid.WalkSpeed
	local currentWalkSpeed = humanoid.WalkSpeed

	-- Calculate the speed multiplier based on WalkSpeed
	local speedMultiplier = currentWalkSpeed / defaultWalkSpeed

	-- Check if the animation has an AnimSpeed modifier
	local animSpeedModifier = 1
	local animationObject = animationTrack.Animation
	if animationObject then
		local animSpeedObject = animationObject:FindFirstChild("AnimSpeed")
		if animSpeedObject and animSpeedObject:IsA("NumberValue") then
			animSpeedModifier = animSpeedObject.Value
		end
	end

	-- Adjust the animation speed based on WalkSpeed and AnimSpeed modifier
	animationTrack:AdjustSpeed(speedMultiplier * animSpeedModifier)
end



local function playWalkAnimation(object, animName)
	local animationTrack = setAnimation(object, animName)

	if animationTrack then
		local humanoid = object:FindFirstChild("Humanoid")
		if humanoid then
			-- Play the walk animation and adjust the speed initially
			animationTrack:Play()
			updateWalkAnimationSpeed(humanoid, animationTrack)

			-- Listen for WalkSpeed changes to adjust animation speed dynamically
			humanoid:GetPropertyChangedSignal("WalkSpeed"):Connect(function()
				updateWalkAnimationSpeed(humanoid, animationTrack)
			end)
		end
	else
		warn("Animation track does not exist")
	end
end

local function playAnimation(object, animName, duration)
	if animName == "WalkAnim" then
		playWalkAnimation(object, animName)
	else
		local animationTrack = setAnimation(object, animName)
		if animationTrack then
			if duration then
				playAnimationForDuration(animationTrack, duration)
			else
				animationTrack:Play()
			end
		else
			warn("Animation track does not exist")
		end
	end
end


-- Function to check if a part is visible
local function isVisible(part)
	return part.Transparency == 0 -- Return true if the part is fully visible
end

-- Function to apply tweening to a part
local function tweenPart(part)
	local tweenInfo = TweenInfo.new(
		0.5,
		Enum.EasingStyle.Cubic,
		Enum.EasingDirection.InOut,
		-1, -- Repeat indefinitely
		true, -- Reverse tweening
		0
	)
	local tween = TweenService:Create(part, tweenInfo, {Transparency = 0.5})
	tween:Play()
end

-- Function to reset the visibility of all parts of an enemy
local function resetHiddenEnemyVisualization(enemy)
	for _, part in ipairs(enemy:GetDescendants()) do
		if (part:IsA("BasePart") or part:IsA("MeshPart")) and part.Name ~= "HumanoidRootPart" then
			part.Transparency = 0 -- Reset transparency to 0
		end
	end
end

-- Function to visualize hidden enemies
local function visualizeHiddenEnemy(enemy)
	local isHiddenValue = enemy.Config:FindFirstChild("IsHidden")
	if isHiddenValue and isHiddenValue.Value then
		-- Tween each descendant's transparency only if they are currently visible
		for _, part in ipairs(enemy:GetDescendants()) do
			if (part:IsA("BasePart") or part:IsA("MeshPart")) and part.Name ~= "HumanoidRootPart" then
				if isVisible(part) then -- Only tween if part is visible
					tweenPart(part) -- Call the tweening function
				end
			end
		end
	else
		-- If not hidden, reset visualization only if IsHidden is true
		if isHiddenValue and isHiddenValue.Value then
			resetHiddenEnemyVisualization(enemy)
		end
	end
end

-- Function to set up the enemy
local function setupEnemy(enemy)
	if enemy:FindFirstChild("Config") then

		if enemy.Config:FindFirstChild("IsHidden") then
			-- Listen for changes to IsHidden
			enemy.Config.IsHidden.Changed:Connect(function()
				visualizeHiddenEnemy(enemy)
			end)
			-- Initial check for visualization
			visualizeHiddenEnemy(enemy)
		end
	end
end

-- Listen for WalkAnim changes and update walk animation live
local function listenForWalkAnimChanges(enemy)
	local animationsFolder = enemy:FindFirstChild("Animations")
	if not animationsFolder then return end
	local function restartWalkAnim()
		playAnimation(enemy, "WalkAnim")
	end
	local walkAnim = animationsFolder:FindFirstChild("WalkAnim")
	if walkAnim then
		walkAnim:GetPropertyChangedSignal("AnimationId"):Connect(restartWalkAnim)
	end
	animationsFolder.ChildAdded:Connect(function(child)
		if child.Name == "WalkAnim" and child:IsA("Animation") then
			restartWalkAnim()
			child:GetPropertyChangedSignal("AnimationId"):Connect(restartWalkAnim)
		end
	end)
end

-- Example of connecting the setup function to enemy spawning
workspace.Enemy.ChildAdded:Connect(function(object)
	setupEnemy(object)
	local cachedSpeed = cacheNormalSpeedValue(object) -- Cache speed
	playAnimation(object, "WalkAnim")
	listenForWalkAnimChanges(object)
	visualizeHiddenEnemy(object)
	object:FindFirstChild("Humanoid").Died:Connect(function()
		onEnemyDeath(object)

		-- Clean up the cache when the enemy dies
		normalSpeedCache[object] = nil
	end)
end)

workspace.Unit.ChildAdded:Connect(function(object)
	setupEnemy(object)
	playAnimation(object, "WalkAnim")
	listenForWalkAnimChanges(object)
	visualizeHiddenEnemy(object)
	object:FindFirstChild("Humanoid").Died:Connect(function()
		onEnemyDeath(object)
	end)
end)

workspace.Towers.ChildAdded:Connect(function(object)
	-- Set default idle animation speed, or adjust as needed
	playAnimation(object, "Idle")
end)

animateTowerEvent.OnClientEvent:Connect(function(tower, animName, target)
	local config = tower:FindFirstChild("Config")
	local humanoid = tower:FindFirstChild("Humanoid")

	if humanoid and config and config:FindFirstChild("Cooldown") then
		-- Get the cooldown duration
		local cooldownDuration = config.Cooldown.Value

		-- Load the animation and get its length
		local animationTrack = setAnimation(tower, animName)

		if animationTrack then
			local animationLength = animationTrack.Length

			-- Check if animation length is longer than cooldown
			if animationLength > cooldownDuration then
				-- Speed up the animation to fit within the cooldown duration
				playAnimationForDuration(animationTrack, cooldownDuration)
			else
				-- Play at normal speed if animation is shorter or equal to cooldown
				playAnimation(tower, animName, animationLength)  -- Speed multiplier set to 1 (normal speed)
			end
		else
			warn("Animation not found for " .. animName)
		end
	else
		-- Play animation with default speed if no cooldown is found
		playAnimation(tower, animName)
	end

	-- Fire projectile if target and trail are present
	if target then
		if tower.Config:FindFirstChild("ProjectileFolder")  then
			if tower.Config.ProjectileFolder:FindFirstChild("ProjectileType") and tower.Config.ProjectileFolder:FindFirstChild("ProjectileType").Value == "Bullet" then
				fireProjectileBullet(tower, target, tower.Config.ProjectileFolder.FadeTime.Value)
			end
		end
	end
end)

animateUnitEvent.OnClientEvent:Connect(function(unit, animName, target)
	local config = unit:FindFirstChild("Config")
	local humanoid = unit:FindFirstChild("Humanoid")

	if humanoid and config and config:FindFirstChild("Cooldown") then
		-- Get the cooldown duration
		local cooldownDuration = config.Cooldown.Value

		-- Load the animation and get its length
		local animationTrack = setAnimation(unit, animName)

		if animationTrack then
			local animationLength = animationTrack.Length

			-- Check if animation length is longer than cooldown
			if animationLength > cooldownDuration then
				-- Speed up the animation to fit within the cooldown duration
				playAnimationForDuration(animationTrack, cooldownDuration)
			else
				-- Play at normal speed if animation is shorter or equal to cooldown
				playAnimation(unit, animName, animationLength)  -- Speed multiplier set to 1 (normal speed)
			end
		else
			warn("Animation not found for " .. animName)
		end
	else
		-- Play animation with default speed if no cooldown is found
		playAnimation(unit, animName)
	end

	-- Fire projectile if target and trail are present
	if target then
		if unit.Config:FindFirstChild("ProjectileFolder")  then
			if unit.Config.ProjectileFolder:FindFirstChild("ProjectileType") and unit.Config.ProjectileFolder:FindFirstChild("ProjectileType").Value == "Bullet" then
				fireProjectileBullet(unit, target, unit.Config.ProjectileFolder.FadeTime.Value)
			end
		end
	end
end)

animateTowerChargeEvent.OnClientEvent:Connect(function(tower, animName, Time)
	local config = tower:FindFirstChild("Config")
	local humanoid = tower:FindFirstChild("Humanoid")

	if humanoid and config and config:FindFirstChild("Cooldown") then
		-- Get the cooldown duration
		local ChargeDuration = Time

		-- Load the animation and get its length
		local animationTrack = setAnimation(tower, animName)

		if animationTrack then
			local animationLength = animationTrack.Length
			playAnimationForDuration(animationTrack, ChargeDuration+0.1)
		else
			warn("Animation not found for " .. animName)
		end
	else
		-- Play animation with default speed if no cooldown is found
		playAnimation(tower, animName)
	end
end)

-- Table to track idle animation per enemy
local enemyIdleTracks = {}

animateEnemyEvent.OnClientEvent:Connect(function(enemy, animName)
	if animName == "IdleAnim" then
		-- Play idle animation in a loop and track it
		local animationTrack = setAnimation(enemy, animName)
		if animationTrack then
			animationTrack.Looped = true
			animationTrack:Play()
			-- Stop previous idle if any
			if enemyIdleTracks[enemy] and enemyIdleTracks[enemy] ~= animationTrack then
				enemyIdleTracks[enemy]:Stop()
			end
			enemyIdleTracks[enemy] = animationTrack
		end
	elseif animName == "StopIdleAnim" then
		-- Stop the tracked idle animation if it exists
		local idleTrack = enemyIdleTracks[enemy]
		if idleTrack then
			idleTrack:Stop()
			enemyIdleTracks[enemy] = nil
		end
	else
		playAnimation(enemy, animName)
	end
	visualizeHiddenEnemy(enemy)
end)

animateEnemyEventDeath.OnClientEvent:Connect(function(enemy,animName, length)
	playAnimation(enemy, animName, length)
end)
