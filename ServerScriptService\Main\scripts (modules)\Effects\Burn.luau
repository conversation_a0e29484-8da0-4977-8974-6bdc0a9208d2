local ReplicatedStorage = game:GetService("ReplicatedStorage")
local DebrisService = game:GetService("Debris")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

local Particles = ReplicatedStorage:WaitForChild("Particles")
-- Remove the circular dependency
-- local EffectManager = require(script.Parent.Parent.EffectManager)

print("Burn effect module loaded!")

local BurnEffect = {}

local burningEnemies = {}
local burnLoopRunning = false

local function startBurnLoop()
	if burnLoopRunning then return end
	burnLoopRunning = true
	task.spawn(function()
		while next(burningEnemies) do
			local now = tick()
			for target, data in pairs(burningEnemies) do
				if not target or not target.Parent or not data.humanoid or data.humanoid.Health <= 0 then
					-- Clean up
					if data.BurnParticle and data.BurnParticle.Parent then
						DebrisService:AddItem(data.BurnParticle, 1)
					end
					burningEnemies[target] = nil
				else
					-- Apply damage if interval passed
					if now - data.lastDamageTime >= data.damageInterval then
						data.lastDamageTime = now
						local actualDamage = math.min(data.humanoid.Health, data.BurnDamage)
						if actualDamage > 0 then
							-- Fix: Use TakeDamage and check for shield logic if needed
							data.humanoid:TakeDamage(actualDamage)
						end
						if data.humanoid.Health <= 0 then
							if data.BurnParticle and data.BurnParticle.Parent then
								DebrisService:AddItem(data.BurnParticle, 1)
							end
							burningEnemies[target] = nil
							continue
						end
					end
					-- Pulse effect
					if data.BurnParticle and data.BurnParticle.Parent then
						data.BurnParticle.Rate = 30 + math.sin((now - data.startTime) * 5) * 20
					end
					-- Check duration
					if now - data.startTime >= data.duration then
						if data.BurnParticle and data.BurnParticle.Parent then
							DebrisService:AddItem(data.BurnParticle, 1)
						end
						burningEnemies[target] = nil
					end
				end
			end
			task.wait(0.1)
		end
		burnLoopRunning = false
	end)
end

function BurnEffect.Apply(tower, target, duration, owner)
	-- Get burn damage from tower config or use default
	local BurnDamage = tower.Config:FindFirstChild("BurnDamage") and tower.Config.BurnDamage.Value or 2

	local humanoid = target:FindFirstChild("Humanoid")
	if not humanoid then 
		return false
	end

	-- Unlike Stun and Dissolve, Burn can affect bosses
	-- But we'll still check for specific immunity
	if target.Config:FindFirstChild("BurnImmune") and target.Config.BurnImmune.Value == true then
		return false
	end

	-- Create fire effect
	local FireEffect = Particles:FindFirstChild("FireEffect")
	if not FireEffect then
		-- Create a default fire effect if none exists
		FireEffect = Instance.new("ParticleEmitter")
		FireEffect.Name = "FireEffect"
		FireEffect.Color = ColorSequence.new({
			ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 200, 0)),
			ColorSequenceKeypoint.new(0.5, Color3.fromRGB(255, 100, 0)),
			ColorSequenceKeypoint.new(1, Color3.fromRGB(200, 0, 0))
		})
		FireEffect.Size = NumberSequence.new({
			NumberSequenceKeypoint.new(0, 0.5),
			NumberSequenceKeypoint.new(0.5, 1),
			NumberSequenceKeypoint.new(1, 0)
		})
		FireEffect.Transparency = NumberSequence.new({
			NumberSequenceKeypoint.new(0, 0.2),
			NumberSequenceKeypoint.new(1, 1)
		})
		FireEffect.Lifetime = NumberRange.new(0.5, 1)
		FireEffect.Rate = 50
		FireEffect.Speed = NumberRange.new(3, 5)
		FireEffect.SpreadAngle = Vector2.new(30, 30)
		FireEffect.Parent = Particles
	end

	-- Find the attachment part
	local attachPart
	if target:FindFirstChild("Torso") then
		attachPart = target.Torso
	elseif target:FindFirstChild("HumanoidRootPart") then
		attachPart = target.HumanoidRootPart
	elseif target:FindFirstChild("UpperTorso") then
		attachPart = target.UpperTorso
	else
		-- Try to find any BasePart to attach to
		for _, part in pairs(target:GetChildren()) do
			if part:IsA("BasePart") then
				attachPart = part
				break
			end
		end
	end

	if not attachPart then 
		return false 
	end

	-- Check if target already has burn effect
	if attachPart:FindFirstChild("BurnEffect") then
		return false -- Already burning
	end

	-- Clone the fire effect for this target
	local BurnParticle = FireEffect:Clone()
	BurnParticle.Name = "BurnEffect"
	BurnParticle.Parent = attachPart
	BurnParticle.Enabled = true

	burningEnemies[target] = {
		humanoid = humanoid,
		BurnDamage = BurnDamage,
		duration = duration,
		owner = owner,
		tower = tower,
		BurnParticle = BurnParticle,
		startTime = tick(),
		lastDamageTime = 0,
		damageInterval = 0.5
	}
	startBurnLoop()
	return true
end

return BurnEffect





