local Fleetfooted = {}

function Fleetfooted.Apply(enemy, enabled)
    print("Applying Fleetfooted modifier to", enemy.Name, "enabled:", enabled)
    
    local humanoid = enemy:FindFirstChild("Humanoid")
    local normalSpeed = enemy.Config:FindFirstChild("NormalSpeed")
    
    if not humanoid or not normalSpeed then
        print("Humanoid or NormalSpeed not found")
        return false
    end
    
    -- Set the config value
    local fleetfooted = enemy.Config:FindFirstChild("Fleetfooted")
    if not fleetfooted then
        fleetfooted = Instance.new("BoolValue")
        fleetfooted.Name = "Fleetfooted"
        fleetfooted.Parent = enemy.Config
    end
    fleetfooted.Value = enabled
    
    -- Apply the speed change
    if enabled then
        humanoid.WalkSpeed = normalSpeed.Value * 2
        
        -- Adjust animation speed if available
        if enemy:FindFirstChild("Animations") and enemy.Animations:FindFirstChild("WalkAnim") then
            local animspeed = enemy.Animations.WalkAnim:FindFirstChild("AnimSpeed") or Instance.new("NumberValue")
            animspeed.Name = "AnimSpeed"
            animspeed.Parent = enemy.Animations.WalkAnim
            animspeed.Value = 2
        end
    else
        humanoid.WalkSpeed = normalSpeed.Value
        
        -- Reset animation speed if available
        if enemy:FindFirstChild("Animations") and enemy.Animations:FindFirstChild("WalkAnim") and enemy.Animations.WalkAnim:FindFirstChild("AnimSpeed") then
            enemy.Animations.WalkAnim.AnimSpeed.Value = 1
        end
    end
    
    return true
end

return Fleetfooted