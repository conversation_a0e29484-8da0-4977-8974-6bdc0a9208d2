local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local Lighting = game:GetService("Lighting")
local events = ReplicatedStorage:WaitForChild("Events")
local musics = ReplicatedStorage:WaitForChild("Music")
local CurrentMusic
local mob = require(script.Parent.Enemy)
local info = workspace.Info
local WaveEnded = info.WaveEnded
local LastWave = info.LastWave
local TweenService = game:GetService("TweenService")

local Wavecount

local round = {}
local votes = {}


local spawnedEnemies = {}  -- Initialize the table to track spawned enemies

local function DestroySpawnBox()
	local spawnbox = workspace:FindFirstChild("SpawnBox")
	if spawnbox then
		for i,part in pairs(spawnbox:GetChildren()) do
			if part:IsA("BasePart") and not part:IsA("SpawnLocation") then
				part:Destroy()
			end
		end
	end
end

-- Function to tween any property of an object
local function TweenProperty(object, duration, property, endValue)
	task.spawn(function()


		local tweenInfo = TweenInfo.new(
			duration,
			Enum.EasingStyle.Linear,         -- Default easing style
			Enum.EasingDirection.Out         -- Default easing direction
		)

		-- Define the goal for the tween: setting `property` to `endValue`
		local goal = {[property] = endValue}

		-- Create and play the tween
		local tween = TweenService:Create(object, tweenInfo, goal)
		tween:Play()

		-- Return the tween object in case you want to manage it later
		return tween
	end)
end
local function CleanAllMusic()
	workspace.Music:ClearAllChildren()
end
local function PlayMusic(Music)
	if CurrentMusic == nil then
		local SelectedMusic = musics:FindFirstChild(Music):Clone()
		SelectedMusic.Parent = workspace.Music
		SelectedMusic:Play()
		CurrentMusic = SelectedMusic
	else
		CurrentMusic:Destroy()
		local SelectedMusic = musics:FindFirstChild(Music):Clone()
		SelectedMusic.Parent = workspace.Music
		SelectedMusic:Play()
		CurrentMusic = SelectedMusic
	end
end
local function StopMusic()
	if CurrentMusic ~= nil then
		TweenProperty(CurrentMusic, 2, "Volume" ,0)
		CurrentMusic:Stop()
		CurrentMusic:Destroy()
		CurrentMusic = nil
	end
end
-- Function to spawn an enemy, applying modifiers individually
local function SpawnEnemy(name, quanity, map, modifiers)
	task.spawn(function()
		if quanity == nil then
			quanity = 1
		end
		for i = 1, quanity do
			local newEnemy = mob.Spawn(name, 1, map, modifiers)[1]  -- Pass modifiers directly

			if newEnemy then
				table.insert(spawnedEnemies, newEnemy)
			end
		end
	end)
end
PlayMusic("Intermission")
-- Function to start the game
function round.StartGame()
	if info.GameRunning.Value == true then return end
	local waves = nil

	local map = round.LoadMap()  -- Load the map
	local mode = round.LoadMode()  -- Load the game mode

	-- Ensure mode and waves are set correctly
	if mode == "Easy" then
		waves = 30
	elseif mode == "Default" then
		waves = 40
	elseif mode == "Determined" then
		waves = 41
	elseif mode == "Unsettling" then
		waves = 24
	else
		-- Default fallback in case mode is not set properly
		warn("No valid mode selected, defaulting to 'Easy' mode.")
		mode = "Easy"
		waves = 30
	end

	Wavecount = waves -- Store wave count for later use
	info.GameRunning.Value = true
	Players.RespawnTime = math.huge
	StopMusic()

	for i = 3, 0, -1 do
		info.Message.Value = "Game starting in..." .. i
		task.wait(1)
	end
	-- Dynamically load all gamemode modules
	local Gamemodes = {}
	local gamemodesFolder = script.Parent:FindFirstChild("Gamemodes")
	if gamemodesFolder then
		for _, module in ipairs(gamemodesFolder:GetChildren()) do
			if module:IsA("ModuleScript") then
				Gamemodes[module.Name] = require(module)
			end
		end
	end
	-- Main wave loop
	for wave = 1, waves do
		info.Wave.Value = wave
		info.Message.Value = ""

		-- Use modular gamemode logic
		local gamemodeFunc = Gamemodes[mode]
		if gamemodeFunc then
			gamemodeFunc(wave, map, {
				SpawnEnemy = SpawnEnemy,
				PlayMusic = PlayMusic,
				StopMusic = StopMusic,
				info = info,
				LastWave = LastWave,
				Wavecount = waves,
			})
		else
			warn("No gamemode function found for mode: " .. tostring(mode))
		end

		-- Wait for the wave to complete
		repeat
			task.wait(1)
		until #workspace.Enemy:GetChildren() == 0 or not info.GameRunning.Value or (info.Min.Value <= 0 and info.Sec.Value <= 0)

		-- Check if the game has ended or if the last wave is finished
		if info.GameRunning.Value and wave == waves then
			info.Message.Value = "VICTORY"
			CleanAllMusic()
		elseif info.GameRunning.Value then
			local reward 
			if mode == "Easy" then
				reward = (50 * wave) * 0.75 / #Players:GetPlayers()
			elseif mode == "Default" then
				reward = (75 * wave) * 0.75 / #Players:GetPlayers()
			elseif mode == "Determined" then
				reward = (125 * wave) * 0.75 / #Players:GetPlayers()
			elseif mode == "Unsettling" then
				reward = (200 * wave) * 0.75 / #Players:GetPlayers()
			end
			for _, player in ipairs(Players:GetPlayers()) do
				player.Gold.Value += reward
			end
			WaveEnded.Value = true
			info.SkipAllowed.Value = false
			info.Message.Value = "Wave Reward: " .. reward
			task.wait(2)
			for i = 5, 0, -1 do
				info.Message.Value = "Next wave starting in..." .. i
				task.wait(1)
			end
		else
			break
		end
	end
end



-- Function to load the map
function round.LoadMap()
	local votedMap = round.ToggleVoting()

	if not votedMap then
		local maps = ServerStorage.Maps:GetChildren()
		votedMap = maps[math.random(1, #maps)].Name
	end

	local mapFolder = ServerStorage.Maps:FindFirstChild(votedMap)
	if not mapFolder then
		mapFolder = ServerStorage.Maps.Template
	end

	local newMap = mapFolder:Clone()
	newMap.Parent = workspace.Map
	if newMap:FindFirstChild("PlayerSpawns") then
		for i, player in pairs(game.Players:GetPlayers()) do
			local spawns = newMap.PlayerSpawns:GetChildren()
			local randomSpawn = spawns[math.random(1, #spawns)]
			if player.Character and randomSpawn then
				player.Character:MoveTo(randomSpawn.Position)
			end
		end
	end
	DestroySpawnBox()
	local LightingDecor = newMap:FindFirstChild("LightingDecor")
	if LightingDecor then
		local sky = LightingDecor:FindFirstChild("Sky")
		if sky then
			sky.Parent = Lighting
		end
		for i, Value in pairs(LightingDecor:GetChildren()) do
			if Value:IsA("NumberValue") or Value:IsA("BoolValue") then
				if Value.Name == "FogEnd" then
					Lighting.FogEnd = Value.Value
				else
					warn("Value doesnt belong to Lighting")
				end
			end
		end
	end
	newMap.Base.Humanoid.HealthChanged:Connect(function(health)
		if health <= 0 then
			info.GameRunning.Value = false
			info.Message.Value = "GAME OVER"
			CleanAllMusic()
		end
	end)

	return newMap
end


function round.LoadMode()
	local votedMode = round.ToggleVotingMode()

	if not votedMode then

		votedMode = "Default"
	end
	info.Mode.Value = votedMode

	local modeFolder = ServerStorage.Modes:FindFirstChild(votedMode)
	if not modeFolder then

		modeFolder = ServerStorage.Modes.Default
	end

	return modeFolder.Name 
end


function round.ToggleVoting()
	local maps = ServerStorage.Maps:GetChildren()
	votes = {}
	for _, map in ipairs(maps) do
		votes[map.Name] = {}
	end

	info.Voting.Value = true

	for i = 15, 1, -1 do
		info.Message.Value = "Map voting (" .. i .. ")"
		task.wait(1)
	end

	local winVote = nil
	local winScore = 0
	for name, map in pairs(votes) do
		if #map > winScore then
			winScore = #map
			winVote = name
		end
	end

	info.Voting.Value = false

	return winVote
end


function round.ToggleVotingMode()
	local modes = ServerStorage.Modes:GetChildren()
	votes = {}
	for _, mode in ipairs(modes) do
		votes[mode.Name] = {}
	end

	info.GamemodeVoting.Value = true

	for i = 5, 1, -1 do
		info.Message.Value = "Gamemode voting (" .. i .. ")"
		task.wait(1)
	end

	local winVote = nil
	local winScore = 0
	for name, mode in pairs(votes) do
		if #mode > winScore then
			winScore = #mode
			winVote = name
		end
	end


	if not winVote then
		local randomMode = modes[math.random(1, #modes)]
		winVote = randomMode.Name
	end

	info.GamemodeVoting.Value = false

	return winVote
end


function round.ProcessVote(player, vote)
	for name, mapVotes in pairs(votes) do
		local oldVote = table.find(mapVotes, player.UserId)
		if oldVote then
			table.remove(mapVotes, oldVote)
			break
		end
	end

	table.insert(votes[vote], player.UserId)

	events:WaitForChild("UpdateVoteCount"):FireAllClients(votes)
end

events:WaitForChild("VoteForMap").OnServerEvent:Connect(round.ProcessVote)

return round
