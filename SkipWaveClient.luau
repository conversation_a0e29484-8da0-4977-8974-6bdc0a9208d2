local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local skip = ReplicatedStorage:WaitFor<PERSON>hild("Skip")

local gui = script.Parent
local Sounds = gui:FindFirstChild("Sounds")

local events = ReplicatedStorage:WaitForChild("Events")
local SkipVoteEvent = events:WaitForChild("SkipVoteEvent")
local showSkip = events:WaitForChild("ShowSkipEvent")

local function PlaySound(Sound)
	if Sounds then
		local Selected_Sound = Sounds:FindFirstChild(Sound)
		if Selected_Sound then
			Selected_Sound:Play()
		end
	end
end
local info = workspace:WaitForChild("Info")
local skipAllowed = info:WaitForChild("SkipAllowed")
local Animator = require(gui.SkipFrame.Animator)
local PlayedAnim = false
local hasVotedThisWave = false -- Track if player has voted this wave
-- Initialize GUI Text
gui.SkipFrame.Skip.Text = "Let: " .. skip.Skip.Value 
gui.SkipFrame.Pass.Text = "Pass: " .. skip.Pass.Value 

-- Function to update the skip text display
local function updateSkipTexts()
	gui.SkipFrame.Skip.Text = "Let: " .. skip.Skip.Value
	gui.SkipFrame.Pass.Text = "Pass: " .. skip.Pass.Value
end

-- Function to handle the visibility of the skip GUI
local function handleSkipVisibility()
	if hasVotedThisWave then
		gui.SkipFrame.Visible = false
		PlayedAnim = false
		return
	end
	if not skipAllowed.Value or workspace.Info.LastWave.Value then
		gui.SkipFrame.Visible = false
		PlayedAnim = false
	elseif info.Min.Value <= 0 and info.Sec.Value <= 0 then
		-- Hide the GUI if the wave ended due to the timer
		gui.SkipFrame.Visible = false
		PlayedAnim = false
	else
		-- Show the GUI if skip is allowed and conditions permit
		if skipAllowed.Value then
			gui.SkipFrame.Visible = true
			if PlayedAnim == false then
				Animator.ShowUp:Play()
				PlayedAnim = true
			end
		end
	end
end

-- Button click events
gui.SkipFrame.Skip.Activated:Connect(function()
	if skipAllowed.Value and not hasVotedThisWave then
		PlaySound("Select")
		SkipVoteEvent:FireServer("Skip")
		gui.SkipFrame.Visible = false -- Hide immediately after voting
		PlayedAnim = false
		hasVotedThisWave = true
	end
end)

gui.SkipFrame.Pass.Activated:Connect(function()
	if not hasVotedThisWave then
		PlaySound("Select")
		SkipVoteEvent:FireServer("Pass")
		gui.SkipFrame.Visible = false -- Hide immediately after voting
		PlayedAnim = false
		hasVotedThisWave = true
	end
end)

-- Update skip text when values change
skip.Skip.Changed:Connect(updateSkipTexts)
skip.Pass.Changed:Connect(updateSkipTexts)

-- Listen for the event to show or hide the skip GUI
showSkip.OnClientEvent:Connect(function(visible)
	if not hasVotedThisWave then
		gui.SkipFrame.Visible = visible
	end
	print("Show Skip GUI: " .. tostring(visible)) -- Debugging line
end)
info.WaveEnded:GetPropertyChangedSignal("Value"):Connect(function()
	gui.SkipFrame.Visible = false -- Hide immediately after voting
	PlayedAnim = false
	hasVotedThisWave = false -- Reset vote flag for new wave
end)
-- Initial visibility check
handleSkipVisibility()

-- Continuously check visibility based on game state
info.Min.Changed:Connect(handleSkipVisibility)
info.Sec.Changed:Connect(handleSkipVisibility)
workspace.Enemy.ChildRemoved:Connect(handleSkipVisibility) -- Trigger when an enemy dies