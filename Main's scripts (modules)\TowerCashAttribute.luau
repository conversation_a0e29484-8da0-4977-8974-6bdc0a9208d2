-- TowerCashAttribute.luau
-- Attribute module for giving cash to the tower owner and showing the GUI when the wave changes.
-- Compatible with TowerAttributesManager: returns a function(attackFunc, tower, config)

local Players = game:GetService("Players")

return function(attackFunc, tower, config)
	local wave = workspace:WaitForChild("Info"):WaitFor<PERSON>hild("Wave")
	local ownerValue = config:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("Owner")
	local cashValue = config:FindFirstChild("FarmCash")
	if not cashValue then
		cashValue = Instance.new("IntValue")
		cashValue.Name = "FarmCash"
		cashValue.Value = 50
		cashValue.Parent = config
	end

	-- Ensure Gather and GUIattachment exist
	local gather = tower:FindFirstChild("Gather")
	if not gather then
		gather = Instance.new("Folder")
		gather.Name = "Gather"
		gather.Parent = tower
	end

	local guiAttachment = gather:FindFirstChild("GUIattachment")
	if not guiAttachment then
		if script:FindFirstChild("GUIattachment") and tower:FindFirstChild("HumanoidRootPart") then
			guiAttachment = script.GUIattachment:Clone()
			guiAttachment.Parent = tower.HumanoidRootPart
			guiAttachment.Name = "GUIattachment"
			guiAttachment.Parent = gather
		end
	end

	if not guiAttachment and tower:FindFirstChild("HumanoidRootPart") then
		-- fallback: create empty attachment
		guiAttachment = Instance.new("Attachment")
		guiAttachment.Name = "GUIattachment"
		guiAttachment.Parent = tower.HumanoidRootPart
		guiAttachment.Parent = gather
	end

	-- Ensure CashSound exists
	if guiAttachment and not guiAttachment:FindFirstChild("CashSound") then
		if script:FindFirstChild("CashSound") then
			local cashSound = script.CashSound:Clone()
			cashSound.Parent = guiAttachment
		end
	end

	wave.Changed:Connect(function()
		while ownerValue.Value == "" do
			task.wait(0.1)
		end
		local player = Players:FindFirstChild(ownerValue.Value)
		if player and player:FindFirstChild("Gold") then
			if guiAttachment and guiAttachment:FindFirstChild("CashSound") then
				guiAttachment.CashSound:Play()
			end
			if guiAttachment and guiAttachment:FindFirstChild("GatherInfo") then
				local gatherInfo = guiAttachment.GatherInfo
				gatherInfo.Visible = true
				gatherInfo.Title.Text = cashValue.Value
				local Animator = require(gatherInfo.Animator)
				Animator.Slide:Play()
				player.Gold.Value += cashValue.Value
				wait(1)
				gatherInfo.Visible = false
			end
		end
	end)

	-- Return the attackFunc unchanged (does not modify attack behavior)
	return attackFunc
end
