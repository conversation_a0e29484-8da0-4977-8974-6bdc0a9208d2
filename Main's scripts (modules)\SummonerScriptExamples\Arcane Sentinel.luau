local enemy = script.Parent
local SummonEvent = game:GetService("ReplicatedStorage").Events.EnemyAbility
local animateEnemyEvent = game:GetService("ReplicatedStorage").Events:WaitForChild("AnimateEnemy")
local cd = enemy.Config.Cooldown.Value

-- Set the enemy's normal speed based on configuration
enemy.Config.NormalSpeed.Value = enemy.Humanoid.WalkSpeed

-- Start the heal and summon cycle after initial delay
task.wait(cd)
SummonEvent:Fire(enemy)


while enemy and enemy.Humanoid.Health > 0 do
	task.wait(cd)
	animateEnemyEvent:FireAllClients(enemy, "SummonAnim")
	-- Fire the summon event
	SummonEvent:Fire(enemy) -- Specific abilities script
end
