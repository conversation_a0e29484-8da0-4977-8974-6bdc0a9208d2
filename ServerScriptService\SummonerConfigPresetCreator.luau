-- SummonerConfigPresetCreator
-- Place this in ServerScriptService. When run, it creates a Folder named 'Config' with all default Summoner values as ValueObjects in ReplicatedStorage.

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local function addValue(parent, class, name, val)
	local v = Instance.new(class)
	v.Name = name
	v.Value = val
	v.Parent = parent
	return v
end

local config = Instance.new("Folder")
config.Name = "Config"

addValue(config, "NumberValue", "Cooldown", 6)
addValue(config, "NumberValue", "SummonCountMin", 3)
addValue(config, "NumberValue", "SummonCountMax", 6)
addValue(config, "StringValue", "SummonName", "Spawn3")
addValue(config, "NumberValue", "SummonDelay", 0.2)
addValue(config, "StringValue", "SummonSound", "")
addValue(config, "StringValue", "SummonAnim", "")
addValue(config, "StringValue", "IdleAnim", "")
addValue(config, "BoolValue", "StopDuringSummon", false)
addValue(config, "NumberValue", "SummonSlowdown", 0)
addValue(config, "BoolValue", "WaitForMinions", false)
addValue(config, "NumberValue", "SummonDistance", 0)

config.Parent = ReplicatedStorage
print("Default Summoner Config folder created in ReplicatedStorage as 'Config'.")
