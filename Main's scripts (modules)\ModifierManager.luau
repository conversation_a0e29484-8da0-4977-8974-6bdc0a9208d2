local ModifierManager = {}
local modifierModules = {}

-- Function to load a modifier module
function ModifierManager.GetModifier(modifierName)
    print("[ModifierManager] Attempting to load modifier module:", modifierName)

    if not modifierModules[modifierName] then
        local modScript = script.Parent.Modifiers:FindFirstChild(modifierName)
        print("[ModifierManager] Searching for module in:", script.Parent.Modifiers:GetFullName(), "Found:", modScript and modScript.Name or 'nil')
        if not modScript then
            print("[ModifierManager] No module found for modifier:", modifierName)
            return nil
        end
        local success, result = pcall(function()
            return require(modScript)
        end)
        if success then
            print("[ModifierManager] Successfully loaded modifier module:", modifierName, result)
            modifierModules[modifierName] = result
        else
            warn("[ModifierManager] Failed to load modifier module:", modifierName, "Error:", result)
            return nil
        end
    else
        print("[ModifierManager] Modifier module already cached:", modifierName, modifierModules[modifierName])
    end

    return modifierModules[modifierName]
end

-- Function to apply a modifier
function ModifierManager.ApplyModifier(enemy, modifierName, enabled, intendedCFrame)
    print("[ModifierManager] Attempting to apply modifier:", modifierName, "to enemy:", enemy and enemy.Name or 'nil', "enabled:", enabled)
    
    if not enemy then
        print("[ModifierManager] Invalid enemy")
        return false
    end
    
    -- Get the modifier module
    local modifierModule = ModifierManager.GetModifier(modifierName)
    print("[ModifierManager] Loaded modifier module for", modifierName, ":", modifierModule)
    if not modifierModule then
        print("[ModifierManager] Modifier module not found:", modifierName)
        return false
    end
    
    if type(modifierModule.Apply) ~= "function" then
        warn("[ModifierManager] Modifier module for", modifierName, "does not have an Apply function!", modifierModule)
        return false
    end
    
    -- If the value is a string, pass it to the Apply function (for things like Armed = "GunName")
    print("[ModifierManager] Calling Apply function for modifier:", modifierName)
    local success, result = pcall(function()
        -- Pass intendedCFrame as third argument if present (for gun logic)
        return modifierModule.Apply(enemy, enabled, intendedCFrame)
    end)
    
    if not success then
        warn("[ModifierManager] Error applying modifier:", modifierName, result)
        return false
    else
        print("[ModifierManager] Modifier Apply returned:", result)
    end
    
    -- If the value is a boolean or number, always set the config value
    if typeof(enabled) == "boolean" or typeof(enabled) == "number" then
        local config = enemy:FindFirstChild("Config")
        if config then
            local configModifier = config:FindFirstChild(modifierName)
            if not configModifier then
                if typeof(enabled) == "boolean" then
                    configModifier = Instance.new("BoolValue")
                elseif typeof(enabled) == "number" then
                    configModifier = Instance.new("NumberValue")
                end
                configModifier.Name = modifierName
                configModifier.Parent = config
            end
            configModifier.Value = enabled
        end
    end
    
    return result
end

-- Function to apply multiple modifiers
function ModifierManager.ApplyModifiers(enemy, modifiers, intendedCFrame)
    if not enemy or not modifiers then
        warn("[ModifierManager] Invalid enemy or modifiers")
        return false
    end
    
    local results = {}
    
    for modifierName, value in pairs(modifiers) do
        print("[ModifierManager] Processing modifier:", modifierName, "value:", value)
        local modModule = ModifierManager.GetModifier(modifierName)
        if modModule and type(modModule.Apply) == "function" then
            print("[ModifierManager] Applying modifier:", modifierName, "to enemy:", enemy.Name, "with value:", value)
            results[modifierName] = ModifierManager.ApplyModifier(enemy, modifierName, value, intendedCFrame)
        else
            -- For non-scripted modifiers, just set the value directly
            local config = enemy:FindFirstChild("Config")
            if config then
                local configModifier = config:FindFirstChild(modifierName)
                if configModifier then
                    print("[ModifierManager] Setting existing config modifier:", modifierName, "to value:", value)
                    configModifier.Value = value
                else
                    local newModifier
                    if typeof(value) == "boolean" then
                        newModifier = Instance.new("BoolValue")
                    elseif typeof(value) == "number" then
                        newModifier = Instance.new("NumberValue")
                    elseif typeof(value) == "string" then
                        newModifier = Instance.new("StringValue")
                    end
                    if newModifier then
                        newModifier.Name = modifierName
                        newModifier.Value = value
                        newModifier.Parent = config
                        print("[ModifierManager] Created new config modifier:", modifierName, "with value:", value)
                    end
                end
            else
                warn("[ModifierManager] Enemy has no Config for modifier:", modifierName)
            end
        end
    end
    
    return results
end

-- Function to call OnDeath for all modifiers with that function, including non-scripted
function ModifierManager.OnDeath(enemy)
    if not enemy then return end
    local config = enemy:FindFirstChild("Config")
    if not config then
        print("[ModifierManager.OnDeath] No Config on enemy:", enemy.Name)
        return
    end

    -- Only require EnemyModule here to avoid circular dependency
    local EnemyModule = require(game.ServerScriptService.Main.Enemy)

    -- Gather all BoolValue/NumberValue modifiers directly under Config
    for _, mod in ipairs(config:GetChildren()) do
        if (mod:IsA("BoolValue") or mod:IsA("NumberValue")) and mod.Value == true then
            print("[ModifierManager.OnDeath] Checking modifier:", mod.Name, "Value:", mod.Value)
            local modModule = ModifierManager.GetModifier(mod.Name)
            if modModule and type(modModule.OnDeath) == "function" then
                print("[ModifierManager.OnDeath] Calling OnDeath for scripted modifier:", mod.Name)
                local result = modModule.OnDeath(enemy)
                -- If OnDeath returns a clone (like petrified), set it up as a normal enemy
                if result and typeof(result) == "Instance" and result:IsA("Model") then
                    EnemyModule.HandleSpawnedEnemy(result)
                    local humanoid = result:FindFirstChildOfClass("Humanoid")
                    if humanoid then
                        humanoid.Died:Connect(function()
                            result:Destroy()
                        end)
                    end
                end
            end
        end
    end
end

-- Utility: Extract all modifiers (BoolValue, NumberValue, StringValue) from a Config folder
function ModifierManager.GetModifiersFromConfig(config)
    local modifiers = {}
    if not config then return modifiers end
    for _, v in ipairs(config:GetChildren()) do
        if v:IsA("BoolValue") or v:IsA("NumberValue") or v:IsA("StringValue") then
            modifiers[v.Name] = v.Value
        end
    end
    print("[ModifierManager] Extracted modifiers from config:", modifiers)
    return modifiers
end

-- Petrified.luau should have a dummy Apply function to silence warnings
-- If you want, you can add this to your Petrified.luau module:
--
-- return {
--     Apply = function(enemy, enabled)
--         return true
--     end,
--     OnDeath = function(enemy)
--         -- ...existing petrification logic...
--     end
-- }

function ModifierManager.WatchStringModifiers(enemy)
    local config = enemy:FindFirstChild("Config")
    if not config then return end
    local function connectStringValue(child)
        if child:IsA("StringValue") then
            print("[ModifierManager] Watching StringValue modifier:", child.Name, "on", enemy.Name)
            child.Changed:Connect(function(newValue)
                print("[ModifierManager] StringValue modifier changed:", child.Name, "=", newValue, "on", enemy.Name)
                local modName = child.Name
                local modModule = ModifierManager.GetModifier(modName)
                if modModule and type(modModule.Apply) == "function" then
                    print("[ModifierManager] Applying StringValue modifier:", modName, "with value:", newValue, "to", enemy.Name)
                    modModule.Apply(enemy, newValue)
                end
            end)
        end
    end
    -- Connect to all existing StringValues
    for _, child in ipairs(config:GetChildren()) do
        connectStringValue(child)
    end
    -- Listen for new StringValues added later
    config.ChildAdded:Connect(function(child)
        connectStringValue(child)
    end)
end

-- Automatically apply all modifiers (including guns) to all enemies at game start and when added
function ModifierManager.AutoApplyAllModifiers()
    local function applyToEnemy(enemy)
        local config = enemy:FindFirstChild("Config")
        if config then
            local modifiers = ModifierManager.GetModifiersFromConfig(config)
            ModifierManager.ApplyModifiers(enemy, modifiers)
            ModifierManager.WatchStringModifiers(enemy)
        end
        -- Listen for Config being added later
        enemy.ChildAdded:Connect(function(child)
            if child.Name == "Config" and child:IsA("Folder") then
                local modifiers = ModifierManager.GetModifiersFromConfig(child)
                ModifierManager.ApplyModifiers(enemy, modifiers)
                ModifierManager.WatchStringModifiers(enemy)
            end
        end)
    end

    local enemyFolder = workspace:FindFirstChild("Enemy")
    if enemyFolder then
        for _, enemy in ipairs(enemyFolder:GetChildren()) do
            applyToEnemy(enemy)
        end
        enemyFolder.ChildAdded:Connect(function(child)
            applyToEnemy(child)
        end)
    end
end

-- Optionally, call this automatically on require (uncomment to enable by default)
ModifierManager.AutoApplyAllModifiers()

return ModifierManager