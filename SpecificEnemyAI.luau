local enemy = script.Parent
local map = workspace:Find<PERSON>irst<PERSON>hild("Map")
local mapFolder = map and map:FindFirstChildOfClass("Folder")
local waypoints = mapFolder and mapFolder:FindFirstChild("Waypoints")
local humanoid = enemy:FindFirstChildOfClass("Humanoid")
local torso = enemy:Find<PERSON>irstChild("Torso")
local clickDetector = torso and torso:FindFirstChildOfClass("ClickDetector")
local dialogueGui = torso and torso:FindFirstChild("DialogueGui")
local movingTo = enemy:WaitForChild("MovingTo", 2) -- Wait for MovingTo to exist

if not (humanoid and waypoints and torso and clickDetector and movingTo) then
	warn("Missing required components for mysterious entity script.")
	return
end

-- Find the middle waypoint
local waypointList = waypoints:GetChildren()
table.sort(waypointList, function(a, b) return tonumber(a.Name) < tonumber(b.Name) end)
local middleIndex = math.ceil(#waypointList / 2)
local middleWaypoint = waypointList[middleIndex]
local middleWaypointNumber = tonumber(middleWaypoint.Name)

-- Stop at the middle waypoint
task.spawn(function()
	while true do
		task.wait(0.1)
		if movingTo.Value == middleWaypointNumber then
			humanoid.WalkSpeed = 0
			if enemy:FindFirstChild("HumanoidRootPart") then
				enemy.HumanoidRootPart.Anchored = true
			end
			break
		end
	end
end)

-- Click logic
local clicked = false
clickDetector.MouseClick:Connect(function()
	if clicked then return end
	enemy.Humanoid.WalkSpeed = 0
	clicked = true

	-- Give every player 350 cash
	for _, player in ipairs(game.Players:GetPlayers()) do
		if player:FindFirstChild("Gold") then
			player.Gold.Value = player.Gold.Value + 350
		end
	end

	-- Show dialogue if available
	if dialogueGui then
		dialogueGui.Enabled = true
	end

	-- Slowly vanish
	for i = 1, 20 do
		for _, part in ipairs(enemy:GetDescendants()) do
			if part:IsA("BasePart") then
				part.Transparency = math.min(1, part.Transparency + 0.05)
			end
		end
		task.wait(0.1)
	end
	enemy:Destroy()
end)