local HttpService = game:GetService("HttpService")
local Enemies = require(game:GetService("ServerScriptService").Main.Enemy)

local DeathSpawner = {}

function DeathSpawner.Setup(enemy)
	if not enemy or not enemy:IsA("Model") then
		warn("[DeathSpawner] Invalid enemy model passed.")
		return
	end

	local config = enemy:FindFirstChild("Config")
	if not config then
		warn("[DeathSpawner] No Config found in enemy: " .. enemy.Name)
		return
	end

	local deathType = config:FindFirstChild("DeathEnemyType")
	if not deathType or deathType.Value == "" then
		warn("[DeathSpawner] No DeathEnemyType defined for: " .. enemy.Name)
		return
	end

	local deathNumber = 1
	if config:Find<PERSON>irstChild("DeathEnemyAmount") then
		deathNumber = config.DeathEnemyAmount.Value
	end

	local modifierString = nil
	if config:FindFirstChild("DeathModifiers") then
		modifierString = config.DeathModifiers.Value
	end

	local modifiers = nil
	if modifierString and modifierString ~= "" then
		local success, result = pcall(function()
			return HttpService:JSONDecode(modifierString)
		end)
		if success then
			modifiers = result
			print("[DeathSpawner] Parsed modifiers for " .. enemy.Name, modifiers)
		else
			warn("[DeathSpawner] Failed to decode DeathModifiers for " .. enemy.Name .. ": " .. tostring(result))
		end
	end

	local map = workspace:FindFirstChild("Map")
	if not map then
		warn("[DeathSpawner] Map not found.")
		return
	end

	local folder = map:FindFirstChildOfClass("Folder")
	if not folder then
		warn("[DeathSpawner] No folder found inside Map.")
		return
	end

	local spawnData = {
		name = deathType.Value,
		quantity = deathNumber,
		modifiers = modifiers,
	}

	print("[DeathSpawner] Enabling death spawn for " .. enemy.Name)
	print("[DeathSpawner] SpawnData:", spawnData)

	Enemies.EnableDeathSpawning(enemy, spawnData, folder)
end

return DeathSpawner
