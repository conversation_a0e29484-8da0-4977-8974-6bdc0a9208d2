-- Industrial Factory Map Setup Script
-- Place this script in ServerScriptService or as a child of the map

local Enemies = require(game:GetService("ServerScriptService").Main.Enemy)

-- Wait for the map to load
local map = workspace.Map:FindFirstChildOfClass("Folder")
if not map then
	warn("Map not found!")
	return
end

-- Define spawn points in the factory
-- You'll need to create these Parts in your map and name them accordingly
local factorySpawnPoints = {
	{
		name = "ConveyorBelt1",
		spawnLocation = map:FindFirstChild("ConveyorSpawn1"), -- Create this Part in your map
		enemyType = "Credulous",
		quantity = 1,
		startWaypoint = 3, -- Start from waypoint 3 (middle of path)
		spawnCondition = "timer",
		interval = 15, -- Spawn every 15 seconds
		enabled = true,
		modifiers = {
			-- Factory workers are slightly faster
			Fleetfooted = false
		}
	},
	{
		name = "EmergencyExit",
		spawnLocation = map:FindFirstChild("EmergencySpawn"), -- Create this Part in your map
		enemyType = "Runner",
		quantity = 2,
		startWaypoint = 1, -- Start from beginning
		spawnCondition = "wave",
		startWave = 5, -- Start spawning from wave 5
		endWave = 15, -- Stop spawning after wave 15
		enabled = true,
		modifiers = {
			-- Emergency evacuees are panicked (faster)
			Fleetfooted = true
		}
	},
	{
		name = "MaintenanceTunnel",
		spawnLocation = map:FindFirstChild("MaintenanceSpawn"), -- Create this Part in your map
		enemyType = "Prepared",
		quantity = 1,
		startWaypoint = 7, -- Start near the end
		spawnCondition = "enemyCount",
		maxEnemies = 5, -- Only spawn if there are 5 or fewer enemies on map
		enabled = true,
		modifiers = {
			-- Maintenance workers are tougher
			Fortified = true
		}
	},
	{
		name = "LoadingDock",
		spawnLocation = map:FindFirstChild("LoadingSpawn"), -- Create this Part in your map
		enemyType = "Armored",
		quantity = 1,
		startWaypoint = 2,
		spawnCondition = "timer",
		interval = 30, -- Spawn every 30 seconds
		enabled = true,
		modifiers = {
			-- Loading dock workers have protective gear
			Fortified = true,
			DefensePercent = 25
		}
	}
}

-- Map spawning configuration
local mapSpawnConfig = {
	checkInterval = 2, -- Check every 2 seconds
	spawnPoints = factorySpawnPoints
}

-- Enable map spawning
Enemies.EnableMapSpawning(map, mapSpawnConfig)

print("Industrial Factory map spawning enabled!")
print("Spawn points configured:")
for _, spawnPoint in pairs(factorySpawnPoints) do
	if spawnPoint.spawnLocation then
		print("- " .. spawnPoint.name .. ": " .. spawnPoint.enemyType .. " (" .. spawnPoint.spawnCondition .. ")")
	else
		warn("Spawn location not found for: " .. spawnPoint.name)
	end
end

-- Optional: Add dynamic spawn points during gameplay
local function addBossSpawnPoint()
	local bossSpawnConfig = {
		name = "BossFactory",
		spawnLocation = map:FindFirstChild("BossSpawn"), -- Create this Part in your map
		enemyType = "Champion",
		quantity = 1,
		startWaypoint = 1,
		spawnCondition = "wave",
		startWave = 10,
		endWave = 10, -- Only spawn on wave 10
		enabled = true,
		modifiers = {
			IsBoss = true,
			Fortified = true,
			DefensePercent = 50
		}
	}
	
	Enemies.AddMapSpawnPoint(map, bossSpawnConfig)
	print("Boss spawn point added!")
end

-- Add boss spawn point after a delay
task.wait(5)
addBossSpawnPoint()

-- Optional: Disable spawning when game ends
local gameInfo = workspace:FindFirstChild("Info")
if gameInfo and gameInfo:FindFirstChild("GameRunning") then
	gameInfo.GameRunning.Changed:Connect(function(isRunning)
		if not isRunning then
			Enemies.DisableMapSpawning(map)
			print("Map spawning disabled - game ended")
		end
	end)
end
