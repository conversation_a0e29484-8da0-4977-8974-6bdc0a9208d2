-- TowerTargeting.luau
-- Shared targeting logic for towers (for both server and client use)

local TowerTargeting = {}

function TowerTargeting.GetEnemyWaypoint(map, enemy, waypointIndex)
	if not map or not map:FindFirstChild("Waypoints") or not enemy or not waypointIndex then
		return nil
	end
	local waypointsFolder = map.Waypoints
	local pathIndex = enemy:GetAttribute("PathIndex")
	if not pathIndex then
		local pathIndexValue = enemy:FindFirstChild("PathIndex")
		if pathIndexValue and pathIndexValue:IsA("IntValue") then
			pathIndex = pathIndexValue.Value
		end
	end
	local pathFolders = {}
	for _, child in ipairs(waypointsFolder:GetChildren()) do
		if child:Is<PERSON>("Folder") and child.Name:match("^Path%d+$") then
			table.insert(pathFolders, child)
		end
	end
	if #pathFolders == 0 then
		for _, child in ipairs(waypointsFolder:GetChildren()) do
			if child:<PERSON><PERSON>("Folder") and child.Name:lower():match("^path$") then
				table.insert(pathFolders, child)
			end
		end
	end
	if #pathFolders == 0 then
		return waypointsFolder:FindFirstChild(tostring(waypointIndex))
	end
	table.sort(pathFolders, function(a, b)
		local na = tonumber(a.Name:match("%d+$")) or 0
		local nb = tonumber(b.Name:match("%d+$")) or 0
		return na < nb
	end)
	local useIndex = pathIndex or 1
	if useIndex < 1 or useIndex > #pathFolders then useIndex = 1 end
	local pathFolder = pathFolders[useIndex]
	return pathFolder and pathFolder:FindFirstChild(tostring(waypointIndex))
end

function TowerTargeting.FindTarget(newTower, range, mode)
	local bestTarget = nil
	local bestWaypoint = nil
	local bestDistance = nil
	local bestHealth = nil
	local map = workspace.Map:FindFirstChildOfClass("Folder")
	local enemies = workspace.Enemy:GetChildren()

	-- Lure prioritization: gather all enemies with Lure == true
	local lureEnemies = {}
	for _, enemy in ipairs(enemies) do
		local lure = enemy.Config:FindFirstChild("Lure")
		if lure and lure.Value == true then
			table.insert(lureEnemies, enemy)
		end
	end
	local targetList = (#lureEnemies > 0) and lureEnemies or enemies

	for _, enemy in ipairs(targetList) do
		local IsHidden = enemy.Config:FindFirstChild("IsHidden")
		local HiddenDetection = newTower.Config.HiddenDetection
		local distanceToEnemy = (enemy.HumanoidRootPart.Position - newTower.HumanoidRootPart.Position).Magnitude
		local waypointPart = TowerTargeting.GetEnemyWaypoint(map, enemy, enemy.MovingTo.Value)
		local distanceToWaypoint = waypointPart and (enemy.HumanoidRootPart.Position - waypointPart.Position).Magnitude or nil
		if distanceToEnemy <= range and enemy.Humanoid.Health > 0 then
			if IsHidden and IsHidden.Value == true and HiddenDetection.Value == true or not IsHidden or IsHidden.Value == false then
				if mode == "Nearest" then
					range = distanceToEnemy
					bestTarget = enemy
				elseif mode == "First" then
					if enemy.MovingTo and enemy.MovingTo.Value and distanceToWaypoint then
						if not bestWaypoint or enemy.MovingTo.Value > bestWaypoint then
							bestWaypoint = enemy.MovingTo.Value
							bestDistance = distanceToWaypoint
							bestTarget = enemy
						elseif enemy.MovingTo.Value == bestWaypoint and distanceToWaypoint < bestDistance then
							bestDistance = distanceToWaypoint
							bestTarget = enemy
						end
					end
				elseif mode == "Last" then
					if enemy.MovingTo and enemy.MovingTo.Value and distanceToWaypoint then
						if not bestWaypoint or enemy.MovingTo.Value < bestWaypoint then
							bestWaypoint = enemy.MovingTo.Value
							bestDistance = distanceToWaypoint
							bestTarget = enemy
						elseif enemy.MovingTo.Value == bestWaypoint and distanceToWaypoint > bestDistance then
							bestDistance = distanceToWaypoint
							bestTarget = enemy
						end
					end
				elseif mode == "Strongest" then
					if enemy.Humanoid and enemy.Humanoid.Health and enemy.MovingTo and enemy.MovingTo.Value then
						if not bestHealth or enemy.Humanoid.Health > bestHealth then
							bestHealth = enemy.Humanoid.Health
							bestWaypoint = enemy.MovingTo.Value
							bestDistance = distanceToWaypoint
							bestTarget = enemy
						elseif enemy.Humanoid.Health == bestHealth then
							if not bestWaypoint or enemy.MovingTo.Value > bestWaypoint then
								bestWaypoint = enemy.MovingTo.Value
								bestTarget = enemy
							elseif enemy.MovingTo.Value == bestWaypoint and distanceToWaypoint and distanceToWaypoint < bestDistance then
								bestDistance = distanceToWaypoint
								bestTarget = enemy
							end
						end
					end
				elseif mode == "Weakest" then
					if enemy.Humanoid and enemy.Humanoid.Health then
						if not bestHealth or enemy.Humanoid.Health < bestHealth then
							bestHealth = enemy.Humanoid.Health
							bestTarget = enemy
						end
					end
				elseif mode == "Random" then
					repeat
						local rng = math.random(#targetList)
						local distanceToMob2 = (targetList[rng].HumanoidRootPart.Position - newTower.HumanoidRootPart.Position).magnitude
						if distanceToMob2 <= range then
							bestTarget = targetList[rng]
						end
					until bestTarget ~= nil
				end
			end
		end
	end
	return bestTarget
end

return TowerTargeting
