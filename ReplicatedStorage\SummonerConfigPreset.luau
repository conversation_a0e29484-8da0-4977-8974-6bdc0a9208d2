-- Summoner Config Preset
-- Place this in ReplicatedStorage. Clone and parent to an enemy as needed.

local config = Instance.new("Folder")
config.Name = "Config"

local function addValue(class, name, val)
	local v = Instance.new(class)
	v.Name = name
	v.Value = val
	v.Parent = config
	return v
end

addValue("NumberValue", "Cooldown", 6)
addValue("NumberValue", "SummonCountMin", 3)
addValue("NumberValue", "SummonCountMax", 6)
addValue("StringValue", "SummonName", "Spawn3")
addValue("NumberValue", "SummonDelay", 0.2)
addValue("StringValue", "SummonSound", "")
addValue("StringValue", "SummonAnim", "")
addValue("StringValue", "IdleAnim", "")
addValue("BoolValue", "StopDuringSummon", false)
addValue("NumberValue", "SummonSlowdown", 0)
addValue("BoolValue", "WaitForMinions", false)
addValue("NumberValue", "SummonDistance", 0)

return config
