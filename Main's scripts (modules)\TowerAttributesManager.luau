-- TowerAttributesManager: Applies and manages tower attributes
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TowerAttributes = script.Parent:WaitForChild("TowerAttributes")

local TowerAttributesManager = {}

-- Loads and applies all enabled attributes from config.Attributes
function TowerAttributesManager.ApplyAttributes(tower, config, attackFunc)
	if not config:FindFirstChild("Attributes") then
		print("[TowerAttributesManager] No Attributes folder found in config.")
		return attackFunc -- No attributes to apply
	end

	local wrappedAttack = attackFunc
	for _, attr in ipairs(config.Attributes:GetChildren()) do
		if attr:IsA("BoolValue") and attr.Value == true then
			print("[TowerAttributesManager] Attribute enabled:", attr.Name)
			local attrModule = TowerAttributes:FindFirstChild(attr.Name)
			if attrModule then
				print("[TowerAttributesManager] Requiring module for attribute:", attr.Name)
				local requireAttr = require(attrModule)
				wrappedAttack = requireAttr(wrappedAttack, tower, config)
			else
				warn("[TowerAttributesManager] Attribute module not found:", attr.Name)
			end
		end
	end
	return wrappedAttack
end

return TowerAttributesManager
