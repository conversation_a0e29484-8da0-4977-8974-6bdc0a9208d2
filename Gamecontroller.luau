local Players = game:GetService("Players")
local PhysicsService = game:GetService("PhysicsService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local allowed = true
local modules = ReplicatedStorage:WaitForChild("Modules")
local health = require(modules:WaitForChild("Health"))

local gold = Players.LocalPlayer:WaitForChild("Gold")
local towers = ReplicatedStorage:WaitForChild("Towers")

local TowerTargeting = require(ReplicatedStorage:WaitForChild("Modules"):WaitForChild("TowerTargetting"))

local events = ReplicatedStorage:WaitForChild("Events")
local messageEvent = events:WaitForChild("MessageEvent")

local functions = ReplicatedStorage:WaitForChild("Functions")
local requestTowerFunction = functions:WaitForChild("RequestTower")
local spawnTowerFunction = functions:WaitForChild("SpawnTower")
local sellTowerFunction = functions:WaitForChild("SellTower")
local changeModeFunction = functions:WaitForChild("ChangeTowerMode")
local getDataFunction = functions:WaitForChild("GetData")

local camera = workspace.CurrentCamera

local gui = script.Parent
local Sounds = gui:FindFirstChild("Sounds")
local BaseSounds = Sounds:FindFirstChild("Base")

local hoverGui = gui:WaitForChild("HoverGui")
local HealthFrame = hoverGui:WaitForChild("HealthFrame")
local CurrentHealth = HealthFrame:WaitForChild("CurrentHealth")
local ShieldHealth = HealthFrame:WaitForChild("ShieldHealth")

local towerHoverGui = gui:WaitForChild("TowerHoverGui")
local Attributes = gui:WaitForChild("Selection"):WaitForChild("Attributes")

local unitHoverGui = gui:WaitForChild("UnitHoverGui")
local unitHealthFrame = unitHoverGui:WaitForChild("HealthFrame")
local unitCurrentHealth = unitHealthFrame:WaitForChild("CurrentHealth")

local info = workspace:WaitForChild("Info")
local Timer = info:WaitForChild("Time")
local Seconds = info:WaitForChild("Sec")
local Minutes = info:WaitForChild("Min")

local finishedAnim = true
local hostilehighlight = nil
local friendlyhighlight = nil
local hoveredInstance = nil
local selectedTower = nil
local towerToSpawn = nil
local canPlace = false
local rotation = 0
local placedTowers = 0
local maxTowers = 20
local lastTouch = tick()
local hoveredEnemy = nil 
local hoveredUnit = nil 
local hoveredTower = nil 
local previousModifiers = {}

local map = workspace.Map:FindFirstChildOfClass("Folder")

local EnemiesAlive
local TowersAlive
local UnitsAlive

local Players = game:GetService("Players")
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local function ClearCameraObjects(name)
	for i, v in pairs(workspace.Camera:GetDescendants()) do
		if v.Name == name then
			v:Destroy()
			print("Removing...")
		end
	end
end
local function PlaySound(Sound)
	if Sounds then
		local Selected_Sound = Sounds:FindFirstChild(Sound)
		if Selected_Sound then
			Selected_Sound:Play()
		end
	end
end
local function ClearHighlights(Set,Highlight)
	for i, object in pairs(workspace[Set]:GetChildren()) do
		if object:FindFirstChild(Highlight) then
			object[Highlight]:Destroy()
		end
	end
end
local function PlayTweenAnimation(object, AnimName)
	for _, instance in ipairs(gui:GetDescendants()) do
		if instance.Name == "Animator" and instance:IsA("ModuleScript") then
			local parentInstance = instance.Parent


			if parentInstance.Name == object then

				local Animator = require(parentInstance.Animator)
				if parentInstance:FindFirstChild("Animator") 
					and parentInstance:FindFirstChild("Animator"):FindFirstChild("Tweens") 
					and parentInstance:FindFirstChild("Animator"):FindFirstChild("Tweens"):FindFirstChild(AnimName) then
					Animator[AnimName]:Play()
				end

			end
		end
	end
end


local function findAncestorWithName(instance, name)
	local current = instance
	while current and current.Parent do
		if current.Parent.Name == name then
			return current.Parent
		end
		current = current.Parent
	end
	return nil
end

local function ServerError(message)
	pcall(function()
		local Template = gui.NotifyTemplate.Template:Clone()
		Template.Parent = gui.NotifyTemplate
		Template.Text = "Error: "..message
		Template.Visible = true
		Template.TextColor3 = Color3.new(1, 0, 0.0156863)
		Template.TextStrokeColor3 = Color3.new(0.454902, 0, 0.00784314)
		PlaySound("Error")
		task.wait(5)
		Template:Destroy()
	end)
end
messageEvent.OnClientEvent:Connect(ServerError)
local function Error(text)
	pcall(function(success)
		local Template = gui.NotifyTemplate.Template:Clone()
		Template.Parent = gui.NotifyTemplate
		Template.Text = "Error: "..text
		Template.Visible = true
		Template.TextColor3 = Color3.new(1, 0, 0.0156863)
		Template.TextStrokeColor3 = Color3.new(0.454902, 0, 0.00784314)
		PlaySound("Error")
		task.wait(5)
		Template:Destroy()
		if not success then
			return
		end
	end)

end
local function UpgradeMessage(text)
	pcall(function()
		PlaySound("Upgrade")
		local Template = gui.NotifyTemplate.Template:Clone()
		Template.Parent = gui.NotifyTemplate
		Template.Text = text
		Template.Visible = true
		Template.TextColor3 = Color3.new(0, 1, 0.0313725)
		Template.TextStrokeColor3 = Color3.new(0, 0.419608, 0)

		task.wait(5)
		Template:Destroy()
	end)
end
local function Message(text)
	pcall(function()
		PlaySound("Message")
		local Template = gui.NotifyTemplate.Template:Clone()
		Template.Parent = gui.NotifyTemplate
		Template.Text = text
		Template.Visible = true
		Template.TextColor3 = Color3.new(1, 1, 1)
		Template.TextStrokeColor3 = Color3.new(0, 0, 0)

		task.wait(5)
		Template:Destroy()
	end)
end



local function setAnimation(object, animName)
	local humanoid = object:WaitForChild("Humanoid")
	local animationsFolder = object:FindFirstChild("Animations")

	if humanoid and animationsFolder then
		local animationObject = animationsFolder:FindFirstChild(animName)

		if animationObject then
			local animator = humanoid:FindFirstChild("Animator") or Instance.new("Animator", humanoid)

			local playingTracks = animator:GetPlayingAnimationTracks()

			for i, track in pairs(playingTracks) do
				if track.Name == animName then
					return track
				end
			end


			local animationTrack = animator:LoadAnimation(animationObject)
			local success, animationTrack

			repeat
				success, animationTrack = pcall(function()
					return animator:LoadAnimation(animationObject)
				end)
				if not success then task.wait(0.2) end
			until success
			return animationTrack
		end
	end
end

local function playAnimation(object, animName)
	local animationTrack = setAnimation(object, animName)
	if animationTrack then
		animationTrack:Play()
	else
		warn("Animation track does not exist")
		return
	end
end


local function MouseRaycast(model)
	local mousePosition = UserInputService:GetMouseLocation()
	local mouseRay = camera:ViewportPointToRay(mousePosition.X, mousePosition.Y)
	local raycastParams = RaycastParams.new()

	local blacklist = camera:GetChildren()
	table.insert(blacklist, model)
	raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
	raycastParams.FilterDescendantsInstances = blacklist

	local raycastResult = workspace:Raycast(mouseRay.Origin, mouseRay.Direction * 1000, raycastParams)

	return raycastResult, mousePosition
end
local function CreateBoundary(tower, placeholder)
	-- do it like CreateRange circle but boundary logic for the tower
	if not tower:FindFirstChild("Config") or not tower.Config:FindFirstChild("BoundarySize") then
		return -- Not a valid tower, do nothing
	end
	local boundarysize = tower.Config.BoundarySize.Value
	local height = (tower.PrimaryPart.Size.Y / 2) + tower.PrimaryPart.Size.Y 
	local offset = Vector3.new(0, -height, 0)
	local boundaryPart = Instance.new("Part")
	boundaryPart.Name = "Boundary"
	boundaryPart.Shape = Enum.PartType.Cylinder
	boundaryPart.Transparency = 0.7
	boundaryPart.BrickColor = BrickColor.new("Persimmon")
	boundaryPart.Size = Vector3.new(0.3, boundarysize, boundarysize)
	boundaryPart.Material = Enum.Material.Neon
	boundaryPart.TopSurface = Enum.SurfaceType.Smooth
	boundaryPart.BottomSurface = Enum.SurfaceType.Smooth
	boundaryPart.CanCollide = false
	boundaryPart.CanQuery = true
	boundaryPart.Parent = tower

	boundaryPart.Position = tower.PrimaryPart.Position + offset
	boundaryPart.CFrame = tower.PrimaryPart.CFrame * CFrame.new(offset)
	boundaryPart.Orientation = Vector3.new(0,0,90)
	
	if placeholder then
		boundaryPart.Anchored = false
		local weld = Instance.new("WeldConstraint")
		weld.Part0 = boundaryPart
		weld.Part1 = tower.PrimaryPart
		weld.Parent = boundaryPart
		boundaryPart.Parent = tower
	else
		boundaryPart.Anchored = true
		boundaryPart.Parent = workspace.Camera
	end
end
local function CreateRangeCircle(tower, placeholder, unit)
	if not tower:FindFirstChild("Config") or not tower.Config:FindFirstChild("Range") then
		return -- Not a valid tower, do nothing
	end
	local range = tower.Config.Range.Value
	local height = (tower.PrimaryPart.Size.Y) - tower.Humanoid.HipHeight
	local offset = CFrame.new(0, -height, 0)

	local p = Instance.new("Part")
	p.Name = "Range"
	p.Shape = Enum.PartType.Cylinder
	p.Material = Enum.Material.Neon
	p.Transparency = 0.9
	p.Color = Color3.new(0.101961, 0.835294, 1)
	p.Size = Vector3.new(0,0.2,0)
	p.TopSurface = Enum.SurfaceType.Smooth
	p.BottomSurface = Enum.SurfaceType.Smooth

	p.CanCollide = false
	local tweeninfo = TweenInfo.new(0.2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, 0, false)
	local tween = TweenService:Create(p, tweeninfo, {Size = Vector3.new(0.2, range * 2, range * 2)})

	if unit == true then
		p.Name = "UnitRange"
	end

	if placeholder then
		p.CFrame = tower.PrimaryPart.CFrame * offset * CFrame.Angles(0, 0, math.rad(90))
		p.Anchored = false
		local weld = Instance.new("WeldConstraint")
		weld.Part0 = p
		weld.Part1 = tower.PrimaryPart
		weld.Parent = p
		p.Parent = tower
		tween:Play()
	elseif unit then
		local scale = tower:GetScale()
		local height = (tower.PrimaryPart.Size.Y) - tower.Humanoid.HipHeight
		local offset = Vector3.new(0, -height, 0)



		p.Position = tower.PrimaryPart.Position + (offset * 1.5)
		p.Orientation = Vector3.new(0,90,90)
		p.Massless = true
		p.Anchored = false
		local weld = Instance.new("WeldConstraint")
		weld.Part0 = p
		weld.Part1 = tower.PrimaryPart
		weld.Parent = p
		p.Parent = workspace.Camera
		tween:Play()
	else
		p.CFrame = tower.PrimaryPart.CFrame * offset * CFrame.Angles(0, 0, math.rad(90))
		p.Anchored = true
		p.Parent = workspace.Camera
		tween:Play()
	end

end

local function GetScale(model)
	-- Check if the model has a PrimaryPart set
	if not model.PrimaryPart then
		warn("Model does not have a PrimaryPart set. Returning default scale.")
		return Vector3.new(1, 1, 1) -- Default scale
	end

	-- Get the scale of the PrimaryPart
	local scale = model.PrimaryPart.Size

	return scale
end

local function addTowerCircle (tower)
	local scale = tower:GetScale()
	local height = (tower.PrimaryPart.Size.Y) - tower.Humanoid.HipHeight
	local offset = Vector3.new(0, -height, 0)
	local circle = ReplicatedStorage.Misc.OwnerCircle:Clone()

	circle.Position = tower.PrimaryPart.Position + (offset * 1.5) 
	circle.Size = Vector3.new(circle.Size.X, circle.Size.Y, (circle.Size.Z * 1.5) * scale)
	circle.Anchored = false
	circle.CanCollide = false
	circle.Parent = tower
	local weld = Instance.new("WeldConstraint")
	weld.Parent = circle
	weld.Part0 = circle
	weld.Part1 = tower.PrimaryPart 



end

local hoveredEnemy = nil
local previousModifiers = {}

local function formatModifierName(name)
	-- Blacklisted prefixes
	local blacklistedPrefixes = {"No"}
	local words = {}

	-- Find capital letters to split words
	for word in name:gmatch("%u%l*") do
		table.insert(words, word)
	end

	local formatted = {}

	for i, word in ipairs(words) do
		if table.find(blacklistedPrefixes, word) then
			-- Keep blacklisted prefix as is
			table.insert(formatted, word)
		else
			-- If not the first word, lower case the first letter
			if i > 1 then
				table.insert(formatted, word:sub(1, 1):lower() .. word:sub(2))
			else
				table.insert(formatted, word) -- Keep the first word as is
			end
		end
	end

	return table.concat(formatted, " ")
end

local function HoverOverMob(result, mousePosition)
	local EnemyHumanoid = result.Instance.Parent:FindFirstChild("Humanoid") or result.Instance.Parent.Parent:FindFirstChild("Humanoid")

	local Blacklist = { NormalSpeed = true,
		Cooldown = true,
		OriginalMaxHealth = true,
		ShieldHealth = true,
		ShieldMaxHealth = true,
		RepeatMoney = true,
		Health = true} 
	local ignoreFolders = { Debuffs = true, Buffs = true }

	if EnemyHumanoid then
		local currentEnemy = EnemyHumanoid.Parent

		-- Update hoverGui position and visibility
		hoverGui.Position = UDim2.new(0, mousePosition.X, 0, mousePosition.Y)
		hoverGui.Visible = true

		-- Update health and title
		local percent = EnemyHumanoid.Health / EnemyHumanoid.MaxHealth
		CurrentHealth.Size = UDim2.new(percent, 0, 0.795, 0) 
		HealthFrame.Health.Text = EnemyHumanoid.Health .. "/" .. EnemyHumanoid.MaxHealth
		hoverGui.Title.Text = currentEnemy.Name

		-- Shield health visualization
		local config = currentEnemy:FindFirstChild("Config")
		if config then
			local shield = config:FindFirstChild("ShieldHealth")
			local shieldMax = config:FindFirstChild("ShieldMaxHealth")
			if shield and shieldMax and shield.Value > 0 and shieldMax.Value > 0 then
				local shieldPercent = shield.Value / shieldMax.Value
				ShieldHealth.Size = UDim2.new(shieldPercent, 0, 0.395, 0)
				HealthFrame.Shield.Text = shield.Value .. "/" .. shieldMax.Value
				ShieldHealth.Visible = true
				HealthFrame.Shield.Visible = true
			else
				HealthFrame.Shield.Visible = false
				ShieldHealth.Visible = false
			end
		end

		-- If already hovering over the same enemy, just update health
		if hoveredEnemy == currentEnemy then
			return
		else
			ClearHighlights("Enemy","HostileHighlight")
			PlaySound("Hover")
		end

		-- Update hovered enemy and create a new highlight
		hoveredEnemy = currentEnemy
		hostilehighlight = ReplicatedStorage.Misc.HostileHighlight:Clone()
		hostilehighlight.Parent = currentEnemy

		-- Clear previous modifier labels
		for _, child in ipairs(hoverGui.Modifiers:GetChildren()) do
			if child.Name == "Modifier" then
				child:Destroy()
			end
		end

		-- Check for modifiers in the enemy's Config
		if config then
			local yOffset = 0 -- Initial offset for the first label

			for _, child in ipairs(config:GetDescendants()) do
				-- Check against the blacklist and ignored folders
				if not Blacklist[child.Name] and not ignoreFolders[child.Parent.Name] then
					-- Only show if the value is not 0 or false
					if child.Value ~= false and child.Value ~= 0 then
						local formattedName = formatModifierName(child.Name)

						-- Create modifier label text based on value type
						local displayText
						if type(child.Value) == "boolean" then
							displayText = formattedName -- Just use the formatted name
						else
							displayText = formattedName .. ": " .. tostring(child.Value) -- Show name and value
						end

						local modifierLabel = hoverGui.Modifiers.ModifierTemplate:Clone()
						modifierLabel.Name = "Modifier"
						modifierLabel.Text = displayText
						modifierLabel.Parent = hoverGui.Modifiers

						-- Adjust the label's position to prevent overlapping
						modifierLabel.Position = UDim2.new(0, 0, 0, yOffset)
						yOffset = yOffset + modifierLabel.Size.Y.Offset -- Increment Y position for the next label

						modifierLabel.Visible = true
					end
				end
			end
			previousModifiers = {} -- Clear previous modifiers, if necessary
		end
	else
		-- If no enemy is being hovered, hide the hoverGui and remove highlight
		ClearHighlights("Enemy","HostileHighlight")
		hoverGui.Visible = false
		hoveredEnemy = nil -- Reset the hovered enemy to allow re-hovering the same enemy
	end
end




local function HoverOverUnit(result, mousePosition)
	local UnitHumanoid = result.Instance.Parent:FindFirstChild("Humanoid") or result.Instance.Parent.Parent:FindFirstChild("Humanoid")

	local Blacklist = { NormalSpeed = true, UnitOwner = true} 
	local ignoreFolders = { Debuffs = false, Buffs = false, ProjectileFolder = false }

	if UnitHumanoid then
		local currentUnit = UnitHumanoid.Parent

		-- Update hoverGui position and visibility
		unitHoverGui.Position = UDim2.new(0, mousePosition.X, 0, mousePosition.Y)
		unitHoverGui.Visible = true

		-- Update health and title
		local percent = UnitHumanoid.Health / UnitHumanoid.MaxHealth
		unitCurrentHealth.Size = UDim2.new(percent, 0, 0.795, 0) 
		unitHealthFrame.Health.Text = UnitHumanoid.Health .. "/" .. UnitHumanoid.MaxHealth
		unitHoverGui.Title.Text = currentUnit.Name

		-- If already hovering over the same enemy, just update health
		if hoveredUnit == currentUnit then
			return
		else

			ClearHighlights("Unit","FriendlyHighlight")
			ClearCameraObjects("UnitRange")
			PlaySound("Hover")
		end

		-- Update hovered enemy
		hoveredUnit = currentUnit
		friendlyhighlight = ReplicatedStorage.Misc.FriendlyHighlight:Clone()
		friendlyhighlight.Parent = currentUnit
		CreateRangeCircle(currentUnit, false, true)

		-- Clear previous modifier labels
		for _, child in ipairs(unitHoverGui.Modifiers:GetChildren()) do
			if child.Name == "Modifier" then
				child:Destroy()
			end
		end

		-- Check for modifiers in the enemy's Config
		local config = currentUnit:FindFirstChild("Config")
		if config then
			local yOffset = 0 -- Initial offset for the first label

			for _, child in ipairs(config:GetDescendants()) do
				-- Check against the blacklist and ignored folders
				if not Blacklist[child.Name] and not child:IsA("Folder") then
					-- Only show if the value is not 0 or false
					if child.Value ~= false and child.Value ~= 0 then
						local formattedName = formatModifierName(child.Name)

						-- Create modifier label text based on value type
						local displayText
						if type(child.Value) == "boolean" then
							displayText = formattedName -- Just use the formatted name
						else
							displayText = formattedName .. ": " .. tostring(child.Value) -- Show name and value
						end

						local modifierLabel = unitHoverGui.Modifiers.ModifierTemplate:Clone()
						modifierLabel.Name = "Modifier"
						modifierLabel.Text = displayText
						modifierLabel.Parent = unitHoverGui.Modifiers

						-- Adjust the label's position to prevent overlapping
						modifierLabel.Position = UDim2.new(0, 0, 0, yOffset)
						yOffset = yOffset + modifierLabel.Size.Y.Offset -- Increment Y position for the next label

						modifierLabel.Visible = true
					end
				end
			end

			previousModifiers = {} -- Clear previous modifiers, if necessary
		end
	else
		-- If no enemy is being hovered, hide the hoverGui and reset the hoveredEnemy
		ClearHighlights("Unit","FriendlyHighlight")
		ClearCameraObjects("UnitRange")
		unitHoverGui.Visible = false
		hoveredUnit = nil -- Reset the hovered enemy to allow re-hovering the same enemy
	end
end 

local function HoverOverTower(result, mousePosition)
	local TowerHumanoid = result.Instance.Parent:FindFirstChild("Humanoid") or result.Instance.Parent.Parent:FindFirstChild("Humanoid")
	if TowerHumanoid then
		local currentTower = TowerHumanoid.Parent
		if hoveredTower == currentTower then
			return
		else
			ClearHighlights("Towers","FriendlyHighlight")
			PlaySound("Hover")
		end

		-- Update hovered enemy
		hoveredTower = currentTower
		friendlyhighlight = ReplicatedStorage.Misc.FriendlyHighlight:Clone()
		friendlyhighlight.Parent = currentTower

		towerHoverGui.TowerName.Text = TowerHumanoid.Parent.Config.OriginalTower.Value
		towerHoverGui.Title.Text = TowerHumanoid.Parent.Config.Owner.Value.."'s "
		towerHoverGui.Targetting.Text = TowerHumanoid.Parent.Config.TargetMode.Value
		towerHoverGui.Position = UDim2.new(0, mousePosition.X, 0, mousePosition.Y)
		towerHoverGui.Visible = true
	else
		ClearHighlights("Towers","FriendlyHighlight")
		hoveredTower = nil
		towerHoverGui.Visible = false
	end
end	

local function RemovePlaceholderTower()
	if towerToSpawn then
		local map = workspace.Map:FindFirstChildOfClass("Folder")
		towerToSpawn:Destroy()
		towerToSpawn = nil
		PlaySound("Cancel")
		rotation = 0
		gui.Controls.Visible = false
		pcall(function()
			for i, Boundary in pairs(map.Boundarys:GetChildren()) do
				for i, BoundaryTower in pairs(workspace.Towers:GetChildren()) do
					for i, Part in pairs(BoundaryTower:GetChildren()) do
						if Part.Name == "Boundary" then
							Part.Transparency = 1
						end
					end
				end
				Boundary.Transparency = 1
			end
		end)
	end
end

local function AddPlaceholderTower(name)
	local map = workspace.Map:FindFirstChildOfClass("Folder")
	local towerExists = towers:FindFirstChild(name)
	if towerExists then
		PlaySound("Select")
		RemovePlaceholderTower()
		towerToSpawn = towerExists:Clone()
		towerToSpawn.Parent = workspace
		playAnimation(towerToSpawn, "Idle")
		if map then
			print("works")
			if map:FindFirstChild("Boundarys") then
				for i, Boundary in pairs(map.Boundarys:GetChildren()) do
					for i, BoundaryTower in pairs(workspace.Towers:GetChildren()) do
						for i, Part in pairs(BoundaryTower:GetChildren()) do
							if Part.Name == "Boundary" then
								Part.Transparency = 0.7
							end
						end
					end
					Boundary.Transparency = 0.7
				end
			end
		end
		CreateRangeCircle(towerToSpawn, true)
		CreateBoundary(towerToSpawn, true)

		for i, object in ipairs(towerToSpawn:GetDescendants()) do
			if object:IsA("BasePart") or object:IsA("UnionOperation") or object:IsA("MeshPart") then
				object.CollisionGroup = "Tower"
				if object.Name ~= "Range" then
					object.Material = Enum.Material.ForceField
					if object.Transparency < 1 then
						object.Transparency = 0.3
					end
				end
			end
		end

		gui.Controls.Visible = true
	end
end

local function ColorPlaceholderTower(color)
	for i, object in ipairs(towerToSpawn:GetDescendants()) do
		if object:IsA("BasePart") then
			object.Color = color
		end
	end
end

local function toggleTowerInfo()
	local OldselectedTower = nil
	workspace.Camera:ClearAllChildren()
	gui.TowerAmount.Text = placedTowers .. "/" .. maxTowers

	if selectedTower and not OldselectedTower then

		CreateRangeCircle(selectedTower)
		if gui.Selection.Visible == false then
			PlayTweenAnimation("Selection","EnterTween")
		end
		gui.Selection.Visible = true
		local oldtowertemple = gui.Selection.ViewPort.ViewportFrame.WorldModel:FindFirstChildOfClass("Model")
		local oldcamera = gui.Selection.ViewPort.ViewportFrame:FindFirstChildOfClass("Camera")
		if oldtowertemple then
			oldtowertemple:Destroy()
			oldcamera:Destroy()
		end
		PlaySound("Toggle1")
		-- remove old Attributes
		for i, OldAttribute in pairs(Attributes:GetChildren()) do
			if  OldAttribute:IsA("TextLabel") and OldAttribute.Visible == true then
				OldAttribute:Destroy()
			end
		end
		local TowerTemple = selectedTower:Clone()
		TowerTemple.Parent = gui.Selection.ViewPort.ViewportFrame.WorldModel
		local Camera = Instance.new("Camera")
		Camera.Parent = gui.Selection.ViewPort.ViewportFrame
		gui.Selection.ViewPort.ViewportFrame.CurrentCamera = Camera


		Camera.CFrame = CFrame.new(TowerTemple.HumanoidRootPart.Position + TowerTemple.HumanoidRootPart.CFrame.LookVector * 3, TowerTemple.HumanoidRootPart.Position)

		Camera.CFrame = TowerTemple.HumanoidRootPart.CFrame * CFrame.Angles(0,math.rad(-225), 0) * CFrame.new(0,1.5,2.1)
		playAnimation(TowerTemple, "Idle")

		local config = selectedTower.Config
		gui.Selection.Stats.Damage.Value.Text = config.Damage.Value
		gui.Selection.Stats.Range.Value.Text = config.Range.Value
		gui.Selection.Stats.Firerate.Value.Text = config.Cooldown.Value
		gui.Selection.Stats.HiddenDetection.Visible = config.HiddenDetection.Value
		gui.Selection.Title.TowerName.Text = config.OriginalTower.Value
		gui.Selection.Title.OwnerName.Text = config.Owner.Value .. "'s"
		gui.Selection.DamageDealt.Text = "Damage dealt: "..config.DamageDealt.Value
		gui.Selection.TotalSpent.Text = "Total spent: "..config.Spent.Value
		gui.Selection.Action.Sell.Title.Text = "Sell: "..math.round((config.Spent.Value / 3))
		if config:FindFirstChild("Attributes") then
			for i, Attribute in pairs(config.Attributes:GetChildren()) do
				if Attribute:IsA("ValueBase") then
					local MiscAttribute = Attributes:FindFirstChild("MiscAttribute"):Clone()
					if Attribute.Value ~= false then
						local valueStr
						if typeof(Attribute.Value) == "boolean" then
							valueStr = tostring(Attribute.Value)
						else
							valueStr = Attribute.Value
						end

						MiscAttribute.Parent = Attributes
						MiscAttribute.Text = Attribute.Name..": "..valueStr
						MiscAttribute.Visible = true
					end
				end
			end
		end
		selectedTower.Config:FindFirstChild("DamageDealt").Changed:Connect(function()
			pcall(function()
				gui.Selection.DamageDealt.Text = "Damage dealt: "..selectedTower.Config.DamageDealt.Value
			end)
		end)

		selectedTower.Config:FindFirstChild("Spent").Changed:Connect(function()
			pcall(function()
				gui.Selection.DamageDealt.Text = "Total spent: "..selectedTower.Config.Spent.Value
			end)
		end)
		local modes = {
			["First"] = "rgb(255,255,255)",
			["Last"] = "rgb(255,255,255)", 
			["Nearest"] = "rgb(255,255,255)",
			["Strongest"] = "rgb(255,255,255)", 
			["Weakest"] = "rgb(255,255,255)",
			["Random"] = "rgb(255,255,255)"
		}
		local color = modes[config.TargetMode.Value]
		gui.Selection.Action.Target.Title.Text = "Target: <font color=\"" .. color .. "\">" .. config.TargetMode.Value .. "</font>"

		if config.Owner.Value == Players.LocalPlayer.Name then
			gui.Selection.Action.Visible = true

			local upgradeTower = config:FindFirstChild("Upgrade")
			if upgradeTower then
				gui.Selection.Action.Upgrade.Visible = true
				gui.Selection.Action.Upgrade.Title.Text = "Upgrade (" .. upgradeTower.Value.Config.Price.Value .. ")"
			else
				gui.Selection.Action.Upgrade.Visible = false
			end
		else

			gui.Selection.Action.Visible = false
		end

	else
		if gui.Selection.Visible == true then
			PlaySound("Toggle2")
			PlayTweenAnimation("Selection","ExitTween")
			finishedAnim = false
		end
		task.wait(0.4)
		if gui.Selection.Visible == true then
			PlayTweenAnimation("Selection","EnterTween")
		end
		gui.Selection.Visible = false
		finishedAnim = true
	end
end



-- Cancel Controls
gui.Controls.Cancel.Activated:Connect(RemovePlaceholderTower)
gui.Controls.Rotate.Activated:Connect(function()
	rotation += 90
end)

UserInputService.InputBegan:Connect(function(key)
	if key.KeyCode == Enum.KeyCode.Q then
		RemovePlaceholderTower()
	end
end)

gui.Selection.Action.Target.Activated:Connect(function()
	if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == false then
		allowed = false
	end
	if allowed == true then
		if selectedTower then
			local modeChangeSuccess = changeModeFunction:InvokeServer(selectedTower)
			if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true and modeChangeSuccess then
				toggleTowerInfo()
				Message("Changed tower priority to: "..selectedTower.Config.TargetMode.Value)
			else
				Error("Cannot change tower priority.")
			end
		end
	else
		Error("Cannot change tower priority")
	end
end)

gui.Selection.Action.Upgrade.Activated:Connect(function()
	if selectedTower.Config:FindFirstChild("Upgrade") then
		if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == false then
			allowed = false
		end
		if allowed == true then
			local upgradeTower = selectedTower.Config.Upgrade.Value
			local upgradeSuccess = spawnTowerFunction:InvokeServer(upgradeTower.Name, selectedTower.PrimaryPart.CFrame, selectedTower)
			if upgradeSuccess then
				selectedTower = upgradeSuccess
				toggleTowerInfo()
				addTowerCircle(selectedTower)
				UpgradeMessage("Upgraded successfully!")
			else
				Error("Tower cannot be upgraded.")
			end
		else
			Error("Tower cannot be upgraded.")
		end

	end
end)

gui.Selection.Action.Sell.Activated:Connect(function()
	if selectedTower then
		if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == false then
			allowed = false
		end
		if allowed == true then

			local soldTower = sellTowerFunction:InvokeServer(selectedTower)
			if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true and soldTower then
				selectedTower = nil
				placedTowers -= 1
				toggleTowerInfo()
				Message("Sold tower.")
			else
				Error("Cant sell.")
			end
		else
			Error("Cant sell.")
		end
	end
end)

local function SpawnNewTower()
	if canPlace then
		local placedTower = spawnTowerFunction:InvokeServer(towerToSpawn.Name, towerToSpawn.PrimaryPart.CFrame)
		if placedTower then
			placedTowers += 1
			selectedTower = placedTower

			addTowerCircle(placedTower)

			RemovePlaceholderTower()
			toggleTowerInfo()
			PlaySound("Place")
		end
	else
		Error("Cant place tower")
	end
end

UserInputService.InputBegan:Connect(function(input, processed)
	if processed then
		return
	end

	-- Function to spawn tower with given index
	local function trySpawnTower(index)
		-- Get player data from the server
		local playerData = getDataFunction:InvokeServer()
		local towerName = playerData.SelectedTowers[index]

		-- Ensure a tower name was provided
		if towerName then
			local tower = towers:FindFirstChild(towerName)

			-- Ensure the tower exists in the workspace
			if tower then
				-- Check if the player is alive before allowing tower placement
				local player = Players.LocalPlayer
				if player and player:FindFirstChild("Alive") and player.Alive.Value then
					local allowedToSpawn = requestTowerFunction:InvokeServer(tower.Name)

					-- If allowed to spawn, add the placeholder tower
					if allowedToSpawn then
						AddPlaceholderTower(tower.Name)
					else
						Error("You cannot place this tower right now.")
					end


				end
			else
				Error("Selected tower does not exist.")
			end
		else
			Error("Invalid tower index.")
		end
	end


	if towerToSpawn then
		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
				SpawnNewTower()
			else
				Error("Cannot place tower while dead.")
			end
		elseif input.UserInputType == Enum.UserInputType.Touch then
			if tick() - lastTouch <= 0.25 then
				if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
					SpawnNewTower()
				else
					Error("Cannot place tower while dead.")
				end
			end
			lastTouch = tick()
		elseif input.KeyCode == Enum.KeyCode.R then
			rotation += 90
			PlaySound("Switch")
		elseif input.KeyCode == Enum.KeyCode.Q then
			RemovePlaceholderTower()
		end
	elseif input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
		-- Improved tower selection logic
		local result, mousePosition = MouseRaycast()
		if result and result.Instance then
			-- Find the tower model by traversing up the hierarchy
			local currentInstance = result.Instance
			local towerModel = nil

			-- Check up to 5 levels up to find a tower model
			for i = 1, 5 do
				if not currentInstance then break end

				-- Check if this is a tower or a parent of a tower
				if currentInstance.Parent == workspace.Towers then
					towerModel = currentInstance
					break
				elseif currentInstance:IsA("Model") and currentInstance:FindFirstChild("Config") and 
					currentInstance:FindFirstChild("Humanoid") and currentInstance.Parent.Parent == workspace.Towers then
					towerModel = currentInstance.Parent
					break
				elseif currentInstance.Parent and currentInstance.Parent:IsA("Model") and 
					currentInstance.Parent:FindFirstChild("Config") and currentInstance.Parent:FindFirstChild("Humanoid") then
					towerModel = currentInstance.Parent
					break
				end

				currentInstance = currentInstance.Parent
			end

			-- Only select if it's a real tower (child of workspace.Towers)
			if towerModel and towerModel.Parent == workspace.Towers then
				selectedTower = towerModel
				toggleTowerInfo()
			else
				if selectedTower then
					selectedTower = nil
					toggleTowerInfo()
				end
			end
		else
			if selectedTower then
				selectedTower = nil
				toggleTowerInfo()
			end
		end
	end

	if input.KeyCode == Enum.KeyCode.E then
		if selectedTower and selectedTower.Config.Owner.Value == Players.LocalPlayer.Name then
			local upgradeTower = selectedTower.Config:FindFirstChild("Upgrade")
			if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
				if upgradeTower then
					-- Ensure upgradeTower.Value is a string
					local towerName = tostring(upgradeTower.Value)  -- Convert to string
					local upgradeSuccess = spawnTowerFunction:InvokeServer(towerName, selectedTower.PrimaryPart.CFrame, selectedTower)
					if upgradeSuccess then
						selectedTower = upgradeSuccess
						toggleTowerInfo()
						addTowerCircle(selectedTower)
						UpgradeMessage("Upgraded successfully!")
					end
				else
					Error("Tower is fully upgraded!")
				end
			else
				Error("Cannot upgrade tower while dead.")
			end
		end

	elseif input.KeyCode == Enum.KeyCode.X then
		if selectedTower and selectedTower.Config.Owner.Value == Players.LocalPlayer.Name then
			if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
				if sellTowerFunction:InvokeServer(selectedTower) then
					selectedTower = nil
					placedTowers -= 1
					toggleTowerInfo()
					Message("Sold tower.")
				end
			else
				Error("Cant sell while dead.")
			end

		end
	elseif input.KeyCode == Enum.KeyCode.Z then
		if selectedTower and selectedTower.Config.Owner.Value == Players.LocalPlayer.Name then
			if changeModeFunction:InvokeServer(selectedTower) then
				toggleTowerInfo()
				Message("Changed tower priority to: "..selectedTower.Config.TargetMode.Value)
			end
		end
	elseif input.KeyCode == Enum.KeyCode.Escape then
		-- Add escape key to deselect tower
		if selectedTower then
			selectedTower = nil
			toggleTowerInfo()
		end
	elseif input.KeyCode == Enum.KeyCode.One then
		if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
			trySpawnTower(1)
		else
			Error("You cannot place while you're dead.")
		end
	elseif input.KeyCode == Enum.KeyCode.Two then
		if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
			trySpawnTower(2)
		else
			Error("You cannot place while you're dead.")
		end
	elseif input.KeyCode == Enum.KeyCode.Three then
		if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
			trySpawnTower(3)
		else
			Error("You cannot place while you're dead.")
		end
	elseif input.KeyCode == Enum.KeyCode.Four then
		if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
			trySpawnTower(4)
		else
			Error("You cannot place while you're dead.")
		end
	elseif input.KeyCode == Enum.KeyCode.Five then
		if Players.LocalPlayer:FindFirstChild("Alive") and Players.LocalPlayer.Alive.Value == true then
			trySpawnTower(5)
		else
			Error("You cannot place while you're dead.")
		end
	end
end)

-- Helper: Get the correct waypoint part for an enemy, supporting multiple paths
local function getEnemyWaypoint(map, enemy, waypointIndex)
	if not map or not map:FindFirstChild("Waypoints") or not enemy or not waypointIndex then
		return nil
	end
	local waypointsFolder = map.Waypoints
	-- Try to get PathIndex from Attribute or IntValue
	local pathIndex = enemy:GetAttribute("PathIndex")
	if not pathIndex then
		local pathIndexValue = enemy:FindFirstChild("PathIndex")
		if pathIndexValue and pathIndexValue:IsA("IntValue") then
			pathIndex = pathIndexValue.Value
		end
	end
	-- Gather all numbered Path folders (Path1, Path2, ...), fallback to any Path
	local pathFolders = {}
	for _, child in ipairs(waypointsFolder:GetChildren()) do
		if child:IsA("Folder") and child.Name:match("^Path%d+$") then
			table.insert(pathFolders, child)
		end
	end
	if #pathFolders == 0 then
		for _, child in ipairs(waypointsFolder:GetChildren()) do
			if child:IsA("Folder") and child.Name:lower():match("^path$") then
				table.insert(pathFolders, child)
			end
		end
	end
	if #pathFolders == 0 then
		-- No path folders, fallback to direct children
		return waypointsFolder:FindFirstChild(tostring(waypointIndex))
	end
	-- Sort pathFolders by number (Path1, Path2, ...)
	table.sort(pathFolders, function(a, b)
		local na = tonumber(a.Name:match("%d+$")) or 0
		local nb = tonumber(b.Name:match("%d+$")) or 0
		return na < nb
	end)
	local useIndex = pathIndex or 1
	if useIndex < 1 or useIndex > #pathFolders then useIndex = 1 end
	local pathFolder = pathFolders[useIndex]
	return pathFolder and pathFolder:FindFirstChild(tostring(waypointIndex))
end

local function FindTarget(newTower, range, mode)
	return TowerTargeting.FindTarget(newTower, range, mode, true)
end 

RunService.RenderStepped:Connect(function()
	local result = MouseRaycast(towerToSpawn)
	if result and result.Instance then
		if towerToSpawn then
			hoveredInstance = nil

			local cameraPositionY = camera.CFrame.Position.Y -- Get the camera's Y position

			-- Use ancestor search to support subfolders
			local towerAreaFolder = findAncestorWithName(result.Instance, "TowerArea")
			if towerAreaFolder then
				local towerAreaNormal = Vector3.new(0, 1, 0)
				local surfaceNormal = result.Normal

				-- Determine placement type
				local placementType = "Ground"
				local TowerToSpawnConfig = towerToSpawn:FindFirstChild("Config")
				if TowerToSpawnConfig and TowerToSpawnConfig:FindFirstChild("PlacementType") and TowerToSpawnConfig.PlacementType:IsA("StringValue") then
					placementType = TowerToSpawnConfig.PlacementType.Value
				end

				-- Find which subfolder (if any) this part is in
				local subAreaType = nil
				for _, subfolder in ipairs(towerAreaFolder:GetChildren()) do
					if subfolder:IsA("Folder") then
						for _, part in ipairs(subfolder:GetChildren()) do
							if part == result.Instance then
								subAreaType = subfolder.Name
								break
							end
						end
						if subAreaType then break end
					end
				end

				-- Placement rules:
				local canPlaceHere = false
				if subAreaType then
					if placementType == subAreaType then
						canPlaceHere = true
					else
						canPlaceHere = false
					end
				else
					canPlaceHere = true -- Not in a subfolder, any type allowed
				end

				-- Check if the surface normal is approximately pointing up
				if (surfaceNormal - towerAreaNormal).Magnitude < 0.1 and canPlaceHere then
					canPlace = true
					ColorPlaceholderTower(Color3.new(0, 0.666667, 0))
				else
					canPlace = false
					ColorPlaceholderTower(Color3.new(0.666667, 0, 0))
				end

				-- Additional check for camera position relative to tower area
				local height = (towerToSpawn.PrimaryPart.Size.Y) - towerToSpawn.Humanoid.HipHeight
				local offset = Vector3.new(0, -height, 0)
				local towerAreaPositionY = result.Position.Y + (towerToSpawn.Humanoid.HipHeight + (towerToSpawn.PrimaryPart.Size.Y * 1.5))
				if cameraPositionY < towerAreaPositionY then
					canPlace = false
					ColorPlaceholderTower(Color3.new(0.666667, 0, 0))
				end
			else
				canPlace = false
				ColorPlaceholderTower(Color3.new(1, 0, 0))
			end

			local height = (towerToSpawn.PrimaryPart.Size.Y) - towerToSpawn.Humanoid.HipHeight
			local offset = Vector3.new(0, -height, 0)
			local x = result.Position.X
			local y = result.Position.Y + (towerToSpawn.Humanoid.HipHeight + (towerToSpawn.PrimaryPart.Size.Y * 1.5))
			local z = result.Position.Z

			local cframe = CFrame.new(x, y, z) * CFrame.Angles(0, math.rad(rotation), 0)
			towerToSpawn:SetPrimaryPartCFrame(cframe)
		else
			hoveredInstance = result.Instance
			EnemiesAlive = #workspace.Enemy:GetChildren()
			if EnemiesAlive > 0 then
				local result, mousePosition = MouseRaycast()
				if result and result.Instance and mousePosition then
					if result.Instance.Parent.Parent and result.Instance.Parent.Parent.Name == "Enemy" then
						HoverOverMob(result, mousePosition)
					elseif result.Instance.Parent.Parent.Parent and result.Instance.Parent.Parent.Parent.Name == "Enemy" then
						HoverOverMob(result, mousePosition)
					else
						ClearHighlights("Enemy","HostileHighlight")
						hoverGui.Visible = false
						hoveredEnemy = nil
					end
				else
					ClearHighlights("Enemy","HostileHighlight")
					hoverGui.Visible = false
					hoveredEnemy = nil
				end
			else
				if hoverGui.Visible == true then
					ClearHighlights("Enemy","HostileHighlight")
					hoverGui.Visible = false
					hoveredEnemy = nil
				end
			end
			UnitsAlive = #workspace.Unit:GetChildren()
			if UnitsAlive > 0 then
				local result, mousePosition = MouseRaycast()
				if result and result.Instance and mousePosition then
					if result.Instance.Parent.Parent and result.Instance.Parent.Parent.Name == "Unit" then
						HoverOverUnit(result, mousePosition)
					elseif result.Instance.Parent.Parent.Parent and result.Instance.Parent.Parent.Parent.Name == "Unit" then
						HoverOverUnit(result, mousePosition)
					else
						ClearCameraObjects("UnitRange")
						ClearHighlights("Unit","FriendlyHighlight")
						unitHoverGui.Visible = false
						hoveredUnit = nil
					end
				else
					ClearCameraObjects("UnitRange")
					ClearHighlights("Unit","FriendlyHighlight")
					unitHoverGui.Visible = false
					hoveredUnit = nil
				end
			else
				if unitHoverGui.Visible == true then
					ClearCameraObjects("UnitRange")
					ClearHighlights("Unit","FriendlyHighlight")
					unitHoverGui.Visible = false
					hoveredUnit = nil
				end
			end
			TowersAlive = #workspace.Towers:GetChildren()
			if TowersAlive > 0 then
				local result, mousePosition = MouseRaycast()
				if result and result.Instance and mousePosition then
					if result.Instance.Parent.Parent and result.Instance.Parent.Parent.Name == "Towers" then
						HoverOverTower(result, mousePosition)
					elseif result.Instance.Parent.Parent.Parent and result.Instance.Parent.Parent.Parent.Name == "Towers" then
						HoverOverTower(result, mousePosition)
					else
						ClearHighlights("Towers","FriendlyHighlight")
						towerHoverGui.Visible = false
						hoveredTower = nil
					end
				else
					ClearHighlights("Towers","FriendlyHighlight")
					towerHoverGui.Visible = false
					hoveredTower = nil
				end
			else
				if towerHoverGui.Visible == true then
					ClearHighlights("Towers","FriendlyHighlight")
					towerHoverGui.Visible = false
					hoveredTower = nil
				end
			end
		end
	else
		hoveredInstance = nil
	end


	if gui.Selection.Visible == true and finishedAnim == true then
		local config = selectedTower.Config
		local target = FindTarget(selectedTower, config. Range.Value, config.TargetMode.Value)

		local enemies = workspace.Enemy:GetChildren()

		ClearHighlights("Enemy","HostileHighlight")
		if target and not target: FindFirstChild("HostileHighlight") then
			local HLT =ReplicatedStorage.Misc.HostileHighlight:Clone()
			HLT.Parent = target
		end

	else

	end
end)




local function DisplayEndScreen(status)
	local screen = gui.EndScreen

	if status == "GAME OVER" then

		screen.Failure:Play()
		screen.FailureSound:Play()
		screen.Content.Title.TextColor3 = Color3.new(1, 0, 0)
		screen.ImageColor3 = Color3.new(0, 0, 0)
		screen.Content.Subtitle.Text = "Overwhelmed by enemies"
		screen.Content.Subtitle.TextColor3 = Color3.new(0.454902, 0, 0)
		screen.Stats.UIStroke.Color = Color3.new(1, 0, 0)
		screen.Stats.StatFrame.UIStroke.Color = Color3.new(0.666667, 0, 0)

	elseif status == "VICTORY" then

		screen.Victory:Play()
		screen.Content.Title.TextColor3 = Color3.new(0, 1, 0)
		screen.ImageColor3 = Color3.new(0, 0.333333, 0)
		screen.Content.Subtitle.Text = "Determination drove you to the victory"
		screen.Content.Subtitle.TextColor3 = Color3.new(0, 0.721569, 0)
		screen.Stats.UIStroke.Color = Color3.new(0, 1, 0)
		screen.Stats.StatFrame.UIStroke.Color = Color3.new(0, 0.666667, 0)

	end
	local info = workspace.Info
	local coins = math.round(info.Wave.Value / 2)
	local true_scale = 1
	if workspace.Map:FindFirstChildOfClass("Folder"):FindFirstChild("Difficulty") then
		local scale = workspace.Map:FindFirstChildOfClass("Folder"):FindFirstChild("Difficulty"):FindFirstChild("RewardScaling").Value or 1
		true_scale = scale
	end

	if info.Message.Value == "VICTORY" then
		if info.Mode.Value =="Easy" then
			coins = 200 * true_scale
		elseif info.Mode.Value == "Default" then
			coins = 500 * true_scale
		elseif info.Mode.Value == "Determined" then
			coins = 1200 * true_scale
		elseif info.Mode.Value == "Unsettling" then
			coins = 5400 * true_scale
		end
	end

	local tweenStyle = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out, 0, false, 0)
	local zoomTween = TweenService:Create(screen, tweenStyle, {Size = UDim2.new(1,0,1,0)})
	zoomTween:Play()
	local tweenInfo = TweenInfo.new(
		2, 
		Enum.EasingStyle.Cubic, 
		Enum.EasingDirection.Out, 
		-1, 
		true, 
		0)
	local tween = TweenService:Create(screen.Stats.UIStroke, tweenInfo, {Thickness = 15})
	tween:Play()
	screen.Content.Title.Text = status
	screen.Stats.StatFrame.Waves.Text = "Wave: " .. workspace.Info.Wave.Value
	screen.Stats.StatFrame.Coins.Text = "Coins: " .. coins
	screen.Stats.StatFrame.Kills.Text = "Kills: " .. Players.LocalPlayer.Kills.Value

	screen.Size = UDim2.new(0,0,0,0)
	screen.Visible = true

end

local function SetupGameGui()
	if not info.GameRunning.Value then
		return
	end

	gui.Voting.Visible = false
	gui.ModeVoting.Visible = false
	gui.Info.Health.Visible = true
	gui.Info.Stats.Visible = true
	gui.Towers.Visible = true
	gui.TowerAmount.Visible = true

	local map = workspace.Map:FindFirstChildOfClass("Folder")
	if map then
		health.Setup(map:WaitForChild("Base"), gui.Info.Health)
	else
		workspace.Map.ChildAdded:Connect(function(newMap)
			health.Setup(newMap:WaitForChild("Base"), gui.Info.Health)
		end)
	end

	info.Wave.Changed:Connect(function(change)
		gui.Info.Stats.Wave.Text = "Wave:" .. change
		PlayTweenAnimation("Wave","ColorChange")
	end)

	gold.Changed:Connect(function(change)
		gui.Info.Stats.Gold.Text = "$" .. gold.Value
	end)
	gui.Info.Stats.Gold.Text = "$" .. gold.Value

	gui.TowerAmount.Text = placedTowers .. "/" .. maxTowers
	local playerData = getDataFunction:InvokeServer()

	for i, tower in pairs(playerData.SelectedTowers) do
		local tower = towers:FindFirstChild(tower)
		if tower then
			local Button = gui.Towers.Template:Clone()
			Button.Name = tower.Name
			Button.Parent = gui.Towers
			Button.Visible = true
			Button.LayoutOrder = i
			Button.Price.Text = tower.Config.Price.Value

			local TowerTemplate = tower:Clone()
			TowerTemplate.Parent = Button.ViewportCharacter.WorldModel
			local ViewportCam = Instance.new("Camera")
			ViewportCam.Parent = Button.ViewportCharacter
			Button.ViewportCharacter.CurrentCamera = ViewportCam

			ViewportCam.CFrame = CFrame.new(TowerTemplate.HumanoidRootPart.Position + TowerTemplate.HumanoidRootPart.CFrame.LookVector * 3, TowerTemplate.HumanoidRootPart.Position)

			ViewportCam.CFrame = TowerTemplate.HumanoidRootPart.CFrame * CFrame.Angles(0,math.rad(-225), 0) * CFrame.new(0,1.1,2.1)

			playAnimation(TowerTemplate, "Idle")

			Button.Activated:Connect(function()
				local allowedToSpawn = requestTowerFunction:InvokeServer(tower.Name)
				if allowedToSpawn then
					Error()
					AddPlaceholderTower(tower.Name)
				end
			end)
		end
	end
end

local function SetupVoteGui()
	if not info.Voting.Value then
		return
	end
	gui.Voting.Visible = true

	local events = ReplicatedStorage:WaitForChild("Events")
	local voteEvent = events:WaitForChild("VoteForMap")
	local voteCountUpdate = events:WaitForChild("UpdateVoteCount")
	local maps = gui.Voting.Maps:GetChildren()
	gui.Voting.Maps.ChildAdded:Connect(function()
		maps = gui.Voting.Maps:GetChildren()
		for i, button in ipairs(maps) do
			if button:IsA("ImageButton") then
				button.Activated:Connect(function()
					voteEvent:FireServer(button.Name)
					PlaySound("Select")
				end)
			end
		end
	end)
	for i, button in ipairs(maps) do
		if button:IsA("ImageButton") then
			button.Activated:Connect(function()
				voteEvent:FireServer(button.Name)
				PlaySound("Select")
			end)
		end
	end

	voteCountUpdate.OnClientEvent:Connect(function(mapScores)
		for name, voteInfo in pairs(mapScores) do
			local button = gui.Voting.Maps:FindFirstChild(name)
			if button then
				button.Vote.Text = #voteInfo
			end
		end
	end)
end
local function SetupVoteModeGui()
	if not info.GamemodeVoting.Value then
		return
	end
	gui.Voting.Visible = false
	gui.ModeVoting.Visible = true

	local events = ReplicatedStorage:WaitForChild("Events")
	local voteEvent = events:WaitForChild("VoteForMap")
	local voteCountUpdate = events:WaitForChild("UpdateVoteCount")
	local maps = gui.ModeVoting.Modes:GetChildren()

	for i, button in ipairs(maps) do
		if button:IsA("ImageButton") then
			button.Activated:Connect(function()
				voteEvent:FireServer(button.Name)
				PlaySound("Select")
			end)
		end
	end

	voteCountUpdate.OnClientEvent:Connect(function(mapScores)
		for name, voteInfo in pairs(mapScores) do
			local button = gui.ModeVoting.Modes:FindFirstChild(name)
			if button then
				button.Vote.Text = #voteInfo
			end
		end
	end)
end


local function LoadGui()
	gui.Info.Message.Text = info.Message.Value

	info.Message.Changed:Connect(function(change)
		gui.Info.Message.Text = change
		if change == "" then
			gui.Info.Message.Visible = false
			if info.WaveEnded.Value == false then
				PlaySound("RoundStart")
			end
		else
			gui.Info.Message.Visible = true
			if info.WaveEnded.Value == true then
				PlaySound("Tick")
			end
			if change == "VICTORY" or change == "GAME OVER" then
				DisplayEndScreen(change)
			end
		end
	end)
	Timer.Changed:Connect(function()
		gui.Info.Stats.Timer.Text = Timer.Value
		if Seconds.Value <= 5 and Minutes.Value == 0 then
			PlaySound("Tick")
			PlayTweenAnimation("Timer","TickEffect")
		end
	end)
	gui.Info.Stats.Timer.Text = Timer.Value
	SetupVoteGui()
	SetupVoteModeGui()
	SetupGameGui()

	info.GameRunning.Changed:Connect(SetupGameGui)
	info.Voting.Changed:Connect(SetupVoteGui)
	info.GamemodeVoting.Changed:Connect(SetupVoteModeGui)
end

LoadGui()
