local Fortified = {}

function Fortified.Apply(enemy, enabled)
    print("Applying Fortified modifier to", enemy.Name, "enabled:", enabled)
    
    local humanoid = enemy:FindFirst<PERSON>hild("Humanoid")
    if not humanoid then
        print("Humanoid not found")
        return false
    end
    
    -- Set the config value
    local fortified = enemy.Config:FindFirstChild("Fortified")
    if not fortified then
        fortified = Instance.new("BoolValue")
        fortified.Name = "Fortified"
        fortified.Parent = enemy.Config
    end
    fortified.Value = enabled
    
    -- Store original max health if not already stored
    if not enemy.Config:FindFirstChild("OriginalMaxHealth") then
        local originalMaxHealth = Instance.new("NumberValue")
        originalMaxHealth.Name = "OriginalMaxHealth"
        originalMaxHealth.Value = humanoid.MaxHealth
        originalMaxHealth.Parent = enemy.Config
    end
    
    -- Apply the health change
    if enabled then
        local healthRatio = humanoid.Health / humanoid.MaxHealth
        humanoid.MaxHealth = enemy.Config.OriginalMaxHealth.Value * 2
        humanoid.Health = humanoid.MaxHealth * healthRatio
    else
        local healthRatio = humanoid.Health / humanoid.MaxHealth
        humanoid.MaxHealth = enemy.Config.OriginalMaxHealth.Value
        humanoid.Health = humanoid.MaxHealth * healthRatio
    end
    
    return true
end

return Fortified