-- ScreenShake.luau
local module = {}

function module.Apply(effectInstance, params)
    local duration = params.duration or 0.5
    local intensity = params.intensity or 1

    -- Only one shake at a time; refresh if already running
    if effectInstance:FindFirstChild("Active") then
        effectInstance.Active.Value = tick() + duration
        return
    end

    local active = Instance.new("NumberValue")
    active.Name = "Active"
    active.Value = tick() + duration
    active.Parent = effectInstance

    local player = game.Players.LocalPlayer
    local camera = workspace.CurrentCamera
    if not camera then return end

    local originalCFrame = camera.CFrame

    -- Shake loop
    task.spawn(function()
        while tick() < active.Value do
            local offset = Vector3.new(
                (math.random() - 0.5) * intensity,
                (math.random() - 0.5) * intensity,
                0
            )
            camera.CFrame = originalCFrame * CFrame.new(offset)
            task.wait(0.03)
        end
        camera.CFrame = originalCFrame
        active:Destroy()
    end)
end

return module