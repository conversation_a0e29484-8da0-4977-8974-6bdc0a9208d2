local mob = require(game.ServerScriptService.Main.Enemy)
local enemy = script.Parent
local config = enemy:FindFirstChild("Config")
local cooldownValue = config and config:FindFirstChild("Cooldown")
local cooldown = (cooldownValue and cooldownValue.Value) or 5
local map = workspace:FindFirstChild("Map") and workspace.Map:FindFirstChildOfClass("Folder")

local normalSpeed = config and config:FindFirstChild("NormalSpeed") and config.NormalSpeed.Value or enemy.Humanoid.WalkSpeed
local spawnedMaD90 = nil

task.wait(cooldown) -- Wait before first spawn

while true do
	if not map then return end
	if not spawnedMaD90 or not spawnedMaD90.Parent then
		-- Calculate spawn position 4-6 studs in front of <PERSON><PERSON>h, offset by <PERSON><PERSON><PERSON>'s own depth and above ground
		local hrp = enemy:FindFirstChild("HumanoidRootPart") or enemy.PrimaryPart
		local forward = hrp.CFrame.LookVector
		local hrpSize = hrp.Size and hrp.Size.Z or 2
		local distance = math.random(400, 600) / 100 + hrpSize -- 4–6 studs plus <PERSON><PERSON><PERSON>'s own depth
		local spawnPos = hrp.Position + forward * distance + Vector3.new(0, hrp.Size.Y / 2, 0)
		local spawnCFrame = CFrame.new(spawnPos, spawnPos + forward)

		-- Spawn MaD-90
		local movingToVal = enemy:FindFirstChild("MovingTo") and enemy.MovingTo.Value or 1
		local pathIndex = enemy:GetAttribute("PathIndex") or (enemy:FindFirstChild("PathIndex") and enemy.PathIndex.Value) or 1
		local spawned = mob.Summon("MaD-90", 1, map, spawnCFrame, movingToVal, pathIndex)
		spawnedMaD90 = spawned and spawned[1]

		-- Make Sloth stationary
		enemy.Humanoid.WalkSpeed = 0

		if spawnedMaD90 and spawnedMaD90:FindFirstChild("Humanoid") then
			spawnedMaD90.Humanoid.Died:Connect(function()
				-- Restore Sloth's speed
				if enemy and enemy.Parent and enemy:FindFirstChild("Humanoid") then
					enemy.Humanoid.WalkSpeed = normalSpeed
				end
				spawnedMaD90 = nil
			end)
		end
	end
	-- Wait for cooldown, but only try to spawn again if MaD-90 is gone
	while spawnedMaD90 and spawnedMaD90.Parent do
		task.wait(1)
	end
	task.wait(cooldown)
end