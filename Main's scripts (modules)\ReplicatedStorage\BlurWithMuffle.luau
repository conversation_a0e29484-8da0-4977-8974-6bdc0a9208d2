-- BlurWithMuffle.luau
local module = {}

function module.Apply(effectInstance, params)
    local duration = params.duration or 2

    -- Blur
    local Lighting = game:GetService("Lighting")
    local blur = Lighting:FindFirstChild("GunBlurEffect") or Instance.new("BlurEffect")
    blur.Name = "GunBlurEffect"
    blur.Size = 16
    blur.Parent = Lighting
    blur.Enabled = true

    -- Muffle
    local SoundService = game:GetService("SoundService")
    local muffle = SoundService:FindFirstChild("GunMuffleEffect") or Instance.new("EqualizerSoundEffect")
    muffle.Name = "GunMuffleEffect"
    muffle.Parent = SoundService
    muffle.HighGain = -80 -- Muffle high frequencies
    muffle.MidGain = 0
    muffle.LowGain = 0
    muffle.Enabled = true

    -- Prevent stacking: refresh timer if already running
    if effectInstance:FindFirstChild("Active") then
        effectInstance.Active.Value = tick() + duration
        return
    end

    local active = Instance.new("NumberValue")
    active.Name = "Active"
    active.Value = tick() + duration
    active.Parent = effectInstance

    -- Cleanup after duration (refreshes if reapplied)
    task.spawn(function()
        while true do
            if tick() >= active.Value then
                break
            end
            task.wait(0.1)
        end
        if blur then blur.Enabled = false end
        if muffle then muffle.Enabled = false end
        active:Destroy()
    end)
end

return module