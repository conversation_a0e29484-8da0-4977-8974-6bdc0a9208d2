-- TowerAbilitiesManager.luau
-- Handles activation and cooldown of tower abilities

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TowerAbilities = script.Parent:WaitForChild("TowerAbilities")

local TowerAbilitiesManager = {}

function TowerAbilitiesManager.ActivateAbility(tower, player)
	local config = tower:FindFirstChild("Config")
	if not config then return false, "No config" end
	local abilityFolder = config:FindFirstChild("Ability")
	if not abilityFolder then return false, "No ability" end
	local nameVal = abilityFolder:FindFirstChild("Name")
	local cooldownVal = abilityFolder:FindFirstChild("Cooldown")
	local activeVal = abilityFolder:FindFirstChild("Active")
	if not (nameVal and cooldownVal and activeVal) then return false, "Missing ability values" end
	if activeVal.Value then return false, "Ability on cooldown" end
	if config:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Owner") and config.Owner.Value ~= player.Name then return false, "Not owner" end

	local abilityModule = TowerAbilities:FindFirstChild(nameVal.Value)
	if not abilityModule then return false, "Ability module not found" end
	local ok, err = pcall(function()
		require(abilityModule).Activate(tower, player)
	end)
	if not ok then return false, err or "Ability error" end

	activeVal.Value = true
	task.delay(cooldownVal.Value, function()
		activeVal.Value = false
	end)
	return true
end

return TowerAbilitiesManager
